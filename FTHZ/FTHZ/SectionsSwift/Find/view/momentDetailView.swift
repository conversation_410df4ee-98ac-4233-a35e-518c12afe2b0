import Foundation
import TTTAttributedLabel

class momentDetailView: UIView {
  public var hzLabelForAlignment: UILabel {
    return herzL
  }
  var onHeightChanged: (() -> Void)?
  enum LikeAnimType {
    case none
    case like
    case unlike
  }
  private let avatarIV: UIImageView
  private let nameL: UILabel
  private let herzL: UILabel
  private let dateL: UILabel
  public let contentL: TTTAttributedLabel
  private let photoView: HZPhotoGroupOld
  private let playerView: FTHZMusicPlayerView
  private let awesomeBtn = UIButton.init()
  private var likesBg: UIView?
  private let videoPlayerView: FTHZVideoPlayerView
  private var avatarViews: [UIImageView] = []

  var likeGroupArr: NSArray = NSArray.init()

  var affair: AffairResult? {
    return detailData
  }

  private let bottomLine: UIView = {
    let line = UIView()
    line.backgroundColor = .fz_extraLightGray
    return line
  }()

  private let commentCountLabel: UILabel = {
    let label = UILabel()
    label.font = .ft_SourceHanserifSC_Meium_12
    label.textColor = .fz_tipTitleColor
    return label
  }()

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  var detailData: AffairResult! {
    didSet {
      setupData()
    }
  }

  override init(frame: CGRect) {
    avatarIV = UIImageView.init(
      frame: CGRect.init(x: 0, y: 0, width: kRealWidth(40), height: kRealWidth(40)))

    nameL = UILabel.init()
    nameL.textColor = .fz_grayBlueColor
    nameL.font = .ft_SourceHanSerif_Blod_14

    herzL = UILabel.init()
    herzL.font = .ft_Din_Bold_12
    herzL.textColor = .fz_lightBlackColor

    dateL = UILabel.init()
    dateL.textColor = .fz_tipTitleColor
    dateL.font = .ft_SourceHanserifSC_Normal_12

    contentL = TTTAttributedLabel(frame: .zero)
    contentL.numberOfLines = 0
    contentL.textColor = .fz_titleBlackColor
    contentL.font = .ft_SourceHanserifSC_Normal_14
    contentL.lineSpacing = kRealWidth(5)

    photoView = HZPhotoGroupOld.init()
    playerView = FTHZMusicPlayerView.init()
    videoPlayerView = FTHZVideoPlayerView()

    super.init(frame: frame)
    contentL.delegate = self

    photoView.delegate = self

    addSubview(avatarIV)
    addSubview(nameL)
    addSubview(herzL)
    addSubview(contentL)
    addSubview(dateL)
    addSubview(bottomLine)
    addSubview(commentCountLabel)

    avatarIV.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.top.equalToSuperview().offset(kRealWidth(20))
      make.size.equalTo(CGSize.init(width: kRealWidth(40), height: kRealWidth(40)))
    }

    nameL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_right).offset(kRealWidth(8))
      make.right.lessThanOrEqualTo(dateL.snp.left).offset(-kRealWidth(8))
      make.top.equalTo(avatarIV.snp_top)
    }

    dateL.snp.makeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.centerY.equalTo(nameL)
      make.height.equalTo(kRealWidth(12))
    }

    herzL.snp.makeConstraints { (make) in
      make.left.right.equalTo(nameL)
      make.bottom.equalTo(avatarIV.snp_bottom)
    }
  }

  func setupData() {
    avatarIV.tio_imageUrl(detailData.avatar, placeHolderImageName: "empty", radius: kRealWidth(14))
    avatarIV.addTapGestureRecognizer { [weak self] in
      let whaleVC = WhaleDetailVC.init()
      if let userid = self?.detailData.authorid {
        whaleVC.uid = userid
        self?.parentContainerViewController?.navigationController?.pushViewController(
          whaleVC, animated: true)
      }
    }

    nameL.text = detailData.nickName
    if let colorM = detailData.colorFont {
      nameL.applyGradient(type: Int(colorM.type), expireTime: Int64(colorM.expire_time))
    }

    herzL.text = "\(detailData.hertz)Hz"

    let contentText =
      detailData.content.count > 0 ? detailData.content : (detailData.type == "4" ? "分享视频" : "分享图片")

    let attributedString = NSMutableAttributedString(string: contentText)

    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = kRealWidth(5)

    attributedString.addAttribute(
      .font, value: UIFont.ft_SourceHanserifSC_Normal_14,
      range: NSRange(location: 0, length: contentText.count))
    attributedString.addAttribute(
      .foregroundColor, value: UIColor.fz_titleBlackColor,
      range: NSRange(location: 0, length: contentText.count))
    attributedString.addAttribute(
      .paragraphStyle, value: paragraphStyle,
      range: NSRange(location: 0, length: contentText.count))

    let regex = try? NSRegularExpression(pattern: "@[^@\\s]+\\s?", options: .caseInsensitive)
    let matches =
      regex?.matches(
        in: contentText, options: [], range: NSRange(location: 0, length: contentText.count)) ?? []

    for (index, match) in matches.enumerated() {
      attributedString.addAttribute(
        .font, value: UIFont.ft_SourceHanSerif_Blod_14, range: match.range)
      attributedString.addAttribute(
        .foregroundColor, value: UIColor.fz_grayBlueColor, range: match.range)
    }

    contentL.setText(attributedString)

    let linkAttributes: [NSAttributedString.Key: Any] = [
      NSAttributedString.Key.font: UIFont.ft_SourceHanSerif_Blod_14,
      NSAttributedString.Key.foregroundColor: UIColor.fz_grayBlueColor,
    ]

    let activeLinkAttributes: [NSAttributedString.Key: Any] = [
      NSAttributedString.Key.font: UIFont.ft_SourceHanSerif_Blod_14,
      NSAttributedString.Key.foregroundColor: UIColor.fz_lightBlackColor,
    ]

    contentL.activeLinkAttributes = activeLinkAttributes

    for (index, match) in matches.enumerated() {
      if index < detailData.atUserIds.count {
        let userId = detailData.atUserIds[index]
        if let url = URL(string: "atuser://\(userId)") {
          let textCheckingResult = NSTextCheckingResult.linkCheckingResult(
            range: match.range, url: url)

          contentL.addLink(with: textCheckingResult, attributes: linkAttributes)
        }
      }
    }

    var lastView: UIView = herzL

    let isVideoType = detailData.type == "4"
    let imgCount = isVideoType ? 1 : NemoUtil.getHZPhotoStringCount(detailData.images)

    if imgCount > 0 {
      addSubview(photoView)

      if isVideoType {
        photoView.getNum(1, index: Int(detailData.imageType) ?? 1)
        photoView.isVideo = true
        photoView.videoURL = detailData.video
      } else {
        photoView.getNum(imgCount, index: Int(detailData.imageType) ?? 1)
      }

      photoView.urlArray = detailData.images.components(separatedBy: ",")

      photoView.snp.makeConstraints { make in
        make.left.equalTo(avatarIV.snp_left)
        make.right.equalToSuperview().offset(-kRealWidth(24))
        make.top.equalTo(lastView.snp.bottom).offset(kRealWidth(12))
        make.height.equalTo(
          NemoUtil.getHZPhotoHeight(imgCount, index: Int(detailData.imageType) ?? 1))
      }
      lastView = photoView
    } else {
      photoView.removeFromSuperview()
    }

    if let musicData: MusicInfoData = detailData.musicContent {
      let hasMusic = musicData.isKind(of: MusicInfoData.self)
      if hasMusic {
        addSubview(playerView)
        playerView.snp.remakeConstraints { (make) in
          make.left.equalTo(avatarIV.snp_left)
          make.width.equalTo(kRealWidth(280))
          make.top.equalTo(lastView.snp.bottom).offset(kRealWidth(12))
          make.height.equalTo(kRealWidth(60))
        }
        playerView.setMusicInfo(musicData, uuid: detailData.contentid, autoTrace: true)
        lastView = playerView
      } else {
        playerView.removeFromSuperview()
      }
    } else {
      playerView.removeFromSuperview()
    }

    contentL.snp.remakeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_left)
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.top.equalTo(lastView.snp.bottom).offset(kRealWidth(12))
    }
    lastView = contentL

    dateL.text = NemoUtil.distanceTimeWith(beforeTime: detailData.createTime.toDouble() ?? 0)
    dateL.snp.remakeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.centerY.equalTo(nameL)
      make.height.equalTo(kRealWidth(12))
    }

    let likeNum = detailData.likeNum.toInt() ?? 0
    if likeNum > 0 {
      AffairLikesModel.getAffairLikesModel(
        detailData.contentid, page: (1), size: "20",
        success: { [weak self] (resObject) in
          let member = AffairLikesModel.mj_object(withKeyValues: resObject)
          if let res = member?.success.boolValue, res == true {
            if let tempData = AffairLikesModelResult.mj_object(withKeyValues: member?.data.first) {
              let tempArr = NSMutableArray.init()
              for dic in tempData.data {
                if let dy = LikesUserModelResult.mj_object(withKeyValues: dic) {
                  tempArr.add(dy)
                }
              }
              self?.likeGroupArr = tempArr.copy() as! NSArray
              self?.setupLikeGroup(lastView: lastView)
            }
          } else {
            self?.showErr()
          }
        }
      ) { [weak self] (Error) in
        self?.showErr()
      }
    } else {
      setupLikeGroup(lastView: lastView)
    }
  }

  func setupLikeGroup(animType: LikeAnimType = .none, lastView: UIView) {
    let count = likeGroupArr.count

    let oldFrames = avatarViews.map { $0.frame }
    let oldUIDs = avatarViews.map { $0.tag }

    if likesBg == nil {
      likesBg = UIView.init()
      likesBg?.backgroundColor = .fz_HighBlackColor
      likesBg?.layer.cornerRadius = kRealWidth(10)
      likesBg?.layer.masksToBounds = true
      addSubview(likesBg!)
    }

    likesBg?.subviews.forEach { $0.removeFromSuperview() }
    avatarViews.removeAll()

    let avatarWidth = kRealWidth(24)
    let avatarSpacing = kRealWidth(12)
    let sidePadding = kRealWidth(10)
    let countLabelWidth = kRealWidth(40)

    let visibleAvatarCount = min(count, 8)
    let totalWidth =
      sidePadding
      + (CGFloat(8) * avatarWidth)
      + (CGFloat(7) * avatarSpacing)
      + sidePadding
      + countLabelWidth

    likesBg?.snp.remakeConstraints { (make) in
      make.left.equalTo(avatarIV.snp_left)
      make.top.equalTo(lastView.snp.bottom).offset(kRealWidth(12))
      make.height.equalTo(count > 0 ? kRealWidth(40) : 0)
      make.width.equalTo(count > 0 ? totalWidth : 0)
    }

    if count > 0 {
      weak var lastAvatarView: UIView?
      for i in (0...likeGroupArr.count - 1) {
        if let dy = likeGroupArr[i] as? LikesUserModelResult, i < 8 {
          let avatarBgView = UIView.init()
          avatarBgView.backgroundColor = .clear
          avatarBgView.layer.cornerRadius = kRealWidth(12)
          avatarBgView.layer.masksToBounds = true
          likesBg?.addSubview(avatarBgView)

          let iv = UIImageView.init()
          iv.tio_imageUrl(dy.avatar, placeHolderImageName: "empty", radius: kRealWidth(10))
          iv.tag = dy.uid.toInt() ?? 0
          iv.layer.cornerRadius = kRealWidth(10)
          iv.clipsToBounds = true
          avatarBgView.addSubview(iv)
          avatarViews.append(iv)

          let finalX = sidePadding + CGFloat(i) * (avatarWidth + avatarSpacing)
          avatarBgView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(finalX)
            make.centerY.equalToSuperview()
            make.size.equalTo(
              CGSize(width: avatarWidth + kRealWidth(1), height: avatarWidth + kRealWidth(1)))
          }

          iv.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSize(width: avatarWidth, height: avatarWidth))
          }

          iv.applySmallShadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(2))

          iv.addTapGestureRecognizer { [weak self] in
            let whale = WhaleDetailVC.init()
            whale.uid = dy.uid
            self?.parentContainerViewController?.navigationController?.pushViewController(
              whale, animated: true)
          }
          lastAvatarView = iv
        } else if i == 8 {
          break
        }
      }

      if lastAvatarView != nil {
        let likeCountLabel = UILabel()
        likeCountLabel.font = .ft_SourceHanserifSC_Blod_12
        likeCountLabel.textColor = .fz_HighBlackColor
        let likeCount = detailData.likeNum.toInt() ?? 0
        likeCountLabel.text = likeCount > 999 ? "999+" : "\(likeCount)"
        likeCountLabel.textAlignment = .center

        let countBgView = UIView()
        countBgView.backgroundColor = .white
        countBgView.layer.masksToBounds = true
        likesBg?.addSubview(countBgView)

        countBgView.addSubview(likeCountLabel)

        let textWidth = (likeCountLabel.text! as NSString).boundingRect(
          with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: kRealWidth(24)),
          options: .usesLineFragmentOrigin,
          attributes: [NSAttributedString.Key.font: likeCountLabel.font!],
          context: nil
        ).width

        countBgView.snp.makeConstraints { make in
          make.centerY.equalToSuperview()
          make.right.equalToSuperview().offset(-sidePadding)
          make.height.equalTo(kRealWidth(20))

          if likeCount < 100 {
            make.width.equalTo(kRealWidth(20))
            countBgView.layer.cornerRadius = kRealWidth(10)
          } else {
            make.width.equalTo(textWidth + kRealWidth(14))
            countBgView.layer.cornerRadius = kRealWidth(10)
          }
        }

        likeCountLabel.snp.makeConstraints { make in
          make.center.equalToSuperview()
          if likeCount < 100 {
            make.left.right.equalToSuperview()
          } else {
            make.left.equalToSuperview().offset(kRealWidth(8))
            make.right.equalToSuperview().offset(-kRealWidth(8))
          }
        }

        let dummyBt = UIButton()
        dummyBt.blockAction { [weak self] (button) in
          self?.gotoLikesPage(self?.detailData.contentid)
        }
        likesBg?.addSubview(dummyBt)
        dummyBt.snp.makeConstraints { (make) in
          make.edges.equalTo(countBgView)
        }
      }
    }

    if detailData.likeRs == "0" {
      awesomeBtn.setImage(UIImage.init(named: "Unlike"), for: .normal)
    } else {
      awesomeBtn.setImage(UIImage.init(named: "like"), for: .normal)
    }

    awesomeBtn.contentEdgeInsets = UIEdgeInsets(
      top: kRealWidth(10),
      left: kRealWidth(10),
      bottom: kRealWidth(10),
      right: kRealWidth(10)
    )

    awesomeBtn.blockAction { [weak self] (button) in
      self?.doLike()
    }

    if awesomeBtn.superview == nil {
      addSubview(awesomeBtn)
    }
    awesomeBtn.snp.remakeConstraints { (make) in
      make.right.equalToSuperview().offset(-kRealWidth(12))
      make.top.equalTo(lastView.snp.bottom).offset(kRealWidth(12))
      make.size.equalTo(CGSize(width: kRealWidth(44), height: kRealWidth(44)))
    }

    let commentCount = detailData.commentNum.toInt() ?? 0
    commentCountLabel.text = "回应 \(commentCount)"

    bottomLine.snp.remakeConstraints { make in
      make.left.equalToSuperview()
      make.right.equalToSuperview()
      make.top.equalTo(awesomeBtn.snp.bottom).offset(kRealWidth(12))
      make.height.equalTo(kRealWidth(4))
    }

    commentCountLabel.snp.remakeConstraints { make in
      make.left.equalTo(avatarIV.snp_left)
      make.top.equalTo(bottomLine.snp.bottom).offset(kRealWidth(12))
      make.height.equalTo(kRealWidth(12))
    }

    self.layoutIfNeeded()

    switch animType {
    case .like:
      if let first = avatarViews.first, first.tag == USERINFO.userid.toInt() {
        first.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)

        for (idx, iv) in avatarViews.enumerated() {
          if idx == 0 { continue }
          if let oldIdx = oldUIDs.firstIndex(of: iv.tag) {
            let dx = oldFrames[oldIdx].origin.x - iv.frame.origin.x
            let dy = oldFrames[oldIdx].origin.y - iv.frame.origin.y
            iv.transform = CGAffineTransform(translationX: dx, y: dy)
          }
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
          for iv in self.avatarViews {
            iv.transform = .identity
          }
          self.layoutIfNeeded()
        } completion: { _ in
          self.onHeightChanged?()
        }
      }

    case .unlike:
      if let removedUID = USERINFO.userid.toInt(),
        let removedIdx = oldUIDs.firstIndex(of: removedUID)
      {
        let removedView = UIImageView(frame: oldFrames[removedIdx])
        removedView.image = avatarViews.first(where: { $0.tag == removedUID })?.image
        removedView.layer.cornerRadius = kRealWidth(10)
        removedView.clipsToBounds = true
        likesBg?.addSubview(removedView)

        for (idx, iv) in avatarViews.enumerated() {
          if let oldIdx = oldUIDs.firstIndex(of: iv.tag) {
            let dx = oldFrames[oldIdx].origin.x - iv.frame.origin.x
            let dy = oldFrames[oldIdx].origin.y - iv.frame.origin.y
            iv.transform = CGAffineTransform(translationX: dx, y: dy)
          }
        }

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
          for iv in self.avatarViews {
            iv.transform = .identity
          }
          removedView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
          self.layoutIfNeeded()
        } completion: { _ in
          removedView.removeFromSuperview()
          self.onHeightChanged?()
        }
      }

    case .none:
      self.layoutIfNeeded()
      self.onHeightChanged?()
    }
  }

  func dealAfterLikeAction(_ res: DoLikeModel) {
    if res.success.boolValue {
      let tempLike = detailData.likeNum.toInt() ?? 0
      let tempLikeGroup = NSMutableArray.init(array: likeGroupArr)

      var lastView: UIView = herzL
      if photoView.superview != nil {
        lastView = photoView
      }
      if playerView.superview != nil {
        lastView = playerView
      }
      lastView = contentL

      if detailData.likeRs == "0" {
        detailData.likeNum = "\(tempLike + 1)"
        detailData.likeRs = "1"
        awesomeBtn.setImage(UIImage.init(named: "like"), for: .normal)

        let likeUser = LikesUserModelResult.init()
        likeUser.uid = USERINFO.userid
        likeUser.avatar = USERINFO.avatar
        likeUser.city = USERINFO.city
        likeUser.gender = USERINFO.gender
        likeUser.hertz = USERINFO.hertz
        likeUser.nickname = USERINFO.nickname
        tempLikeGroup.insert(likeUser, at: 0)

        likeGroupArr = tempLikeGroup.copy() as! NSArray
        setupLikeGroup(animType: .like, lastView: lastView)

      } else {
        detailData.likeNum = "\(tempLike - 1)"
        detailData.likeRs = "0"
        awesomeBtn.setImage(UIImage.init(named: "Unlike"), for: .normal)

        for likeUser in likeGroupArr {
          if let data = likeUser as? LikesUserModelResult,
            data.uid == USERINFO.userid
          {
            tempLikeGroup.remove(data)
            break
          }
        }

        likeGroupArr = tempLikeGroup.copy() as! NSArray
        setupLikeGroup(animType: .unlike, lastView: lastView)
      }

      if let tableView = self.superview as? UITableView {
        tableView.beginUpdates()
        tableView.endUpdates()
      }
    } else if res.code.intValue == 1004 {
      FTHZAlertDialogController.showBannedTip(withMessage: res.msg)
    } else {
      showErr()
    }
  }

  private func updateLayout() {
    likesBg?.snp.remakeConstraints { make in
      make.left.equalTo(avatarIV.snp_left)
      make.right.equalTo(contentL.snp_right)
      make.top.equalTo(contentL.snp_bottom).offset(kRealWidth(12))
      make.height.equalTo(likeGroupArr.count > 0 ? kRealWidth(40) : 0)
    }

    awesomeBtn.snp.remakeConstraints { make in
      make.right.equalTo(contentL.snp_right)
      make.top.equalTo(likesBg?.snp_bottom ?? contentL.snp_bottom).offset(kRealWidth(12))
      make.width.greaterThanOrEqualTo(kRealWidth(80))
      make.height.equalTo(kRealWidth(44))
    }

    UIView.animate(withDuration: 0.3) {
      self.layoutIfNeeded()
    }
  }

  func gotoLikesPage(_ contentId: String!) {
    let likeListVC = FZMomentLikeListVC.init()
    likeListVC.contentId = contentId
    parentContainerViewController?.navigationController?.pushViewController(
      likeListVC, animated: true)
  }

  func doLike() {
    if detailData.upvoted() {
      DoLikeModel.postUnlikeModel(
        detailData.authorid, contentid: detailData.contentid,
        success: { [weak self] (res) in
          if let resObj = DoLikeModel.mj_object(withKeyValues: res) {
            self?.dealAfterLikeAction(resObj)
          } else {
            self?.showErr()
          }
        }
      ) { [weak self] (err) in
        self?.showErr()
      }
    } else {
      DoLikeModel.post(
        detailData.authorid, contentid: detailData.contentid,
        success: { [weak self] (res) in
          if let resObj = DoLikeModel.mj_object(withKeyValues: res) {
            self?.dealAfterLikeAction(resObj)
          } else {
            self?.showErr()
          }
        }
      ) { [weak self] (err) in
        self?.showErr()
      }
    }
  }

  func showErr() {
    makeToast("数据有误，请检查网络后重试", duration: 1.0, position: CSToastPositionCenter)
  }

  func getCurrentHeight() -> CGFloat {
    return commentCountLabel.frame.maxY
  }
}

extension momentDetailView: HZPhotoGroupOldDelegate {
  func photoGroup(_ photoGroup: HZPhotoGroupOld, didTapVideoWithVideoURL videoURL: String) {
    guard let url = URL(string: videoURL) else { return }
    let playerVC = FTHZFullScreenVideoPlayerViewController(videoURL: url)
    playerVC.modalPresentationStyle = .overFullScreen
    parentContainerViewController?.present(playerVC, animated: true)
  }
}

extension momentDetailView: TTTAttributedLabelDelegate {
  func attributedLabel(_ label: TTTAttributedLabel!, didSelectLinkWith url: URL!) {
    if url.scheme == "atuser", let userId = url.host {
      let whaleVC = WhaleDetailVC.init()
      whaleVC.uid = userId
      self.parentContainerViewController?.navigationController?.pushViewController(
        whaleVC, animated: true)
    }
  }
  func attributedLabel(
    _ label: TTTAttributedLabel!, shouldFollowLink url: URL!, with range: NSRange
  ) -> Bool {
    return true
  }
}
