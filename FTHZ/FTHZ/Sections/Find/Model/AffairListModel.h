#import "BaseJsonModel.h"
#import "MusicInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UserResult <NSObject>
@end
@interface UserResult : BaseJsonModel
@property(nonatomic, strong) NSString<Optional> *uid;
@property(nonatomic, strong) NSString<Optional> *nickname;
@property(nonatomic, strong) NSString<Optional> *signature;
@property(nonatomic, strong) NSString<Optional> *gender;
@property(nonatomic, strong) NSString<Optional> *avatar;
@property(nonatomic, strong) NSString<Optional> *city;
@property(nonatomic, strong) NSString<Optional> *hertz;
@property(nonatomic, strong) ColorFont *colorFont;

@end

@protocol AffairResult <NSObject>
@end
@interface AffairResult : BaseJsonModel
@property(nonatomic, strong) NSString<Optional> *aid;
@property(nonatomic, strong) NSString<Optional> *status;
@property(nonatomic, strong) NSString<Optional> *timing;
@property(nonatomic, strong) NSString<Optional> *content;
@property(nonatomic, strong) NSString<Optional> *images;
@property(nonatomic, strong) NSString<Optional> *video;
@property(nonatomic, strong) NSString<Optional> *type;
@property(nonatomic, strong) NSString<Optional> *likeNum;
@property(nonatomic, strong) NSString<Optional> *commentNum;
@property(nonatomic, strong) NSString<Optional> *likeRs;
@property(nonatomic, strong) NSString<Optional> *commentRs;
@property(nonatomic, strong) NSString<Optional> *dateline;
@property(nonatomic, strong) NSString<Optional> *imageType;
@property(nonatomic, strong) NSString<Optional> *tagType;
@property(nonatomic, strong) NSString<Optional> *tagName;
@property(nonatomic, strong) NSString<Optional> *sponsor;
@property(nonatomic, strong) NSString<Optional> *top;
@property(nonatomic, strong) NSString<Optional> *avatar;
@property(nonatomic, strong) NSString<Optional> *nickName;
@property(nonatomic, strong) NSString<Optional> *gender;
@property(nonatomic, strong) NSString<Optional> *signature;
@property(nonatomic, strong) NSString<Optional> *city;
@property(nonatomic, strong) NSString<Optional> *createTime;
@property(nonatomic, strong) NSString<Optional> *create_time;
@property(nonatomic, strong) NSString<Optional> *hertz;
@property(nonatomic, strong) NSString<Optional> *authorid;
@property(nonatomic, strong) NSString<Optional> *contentid;
@property(nonatomic, strong, nullable) MusicInfoData *musicContent;
@property(nonatomic, strong, nullable) ColorFont *colorFont;
@property(nonatomic, strong) NSArray<NSString *><Optional> *atUserIds;

- (NSArray<NSString *> *_Nullable)photoURLs;
- (NSInteger)upvoteCount;
- (NSInteger)commentCount;
- (BOOL)upvoted;

@end

@protocol DynamicModelResult <NSObject>
@end
@interface DynamicModelResult : BaseJsonModel
@property(nonatomic, strong) UserResult *user;
@property(nonatomic, strong) AffairResult *affair;
@end

@protocol AffairListModelResult <NSObject>
@end
@interface AffairListModelResult : BaseJsonModel
@property(nonatomic, strong) NSString *page;
@property(nonatomic, strong) NSString *size;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, assign) NSInteger lastId;
@property(nonatomic, strong) NSArray<DynamicModelResult *> *data;
@end

@protocol NewDynamicModelResult <NSObject>
@end
@interface NewDynamicModelResult : BaseJsonModel
@property(nonatomic, strong) NSNumber *number;
@property(nonatomic, strong) NSNumber *page;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, strong) NSString *lastId;
@property(nonatomic, strong) NSArray<DynamicModelResult *> *data;
@end

@interface AffairListModel : BaseJsonModel
@property(nonatomic, strong) NSArray<NewDynamicModelResult *> *data;
@property(nonatomic, strong) NSNumber *number;
@property(nonatomic, strong) NSNumber *page;
@property(nonatomic, strong) NSString *count;
@property(nonatomic, strong) NSString *lastId;

+ (void)getAffairListModel:(int)page
                   herzNum:(int)herzNum
                   success:(success)_success
                   failure:(failure)_failure;

+ (void)getAffairListNewModel:(NSString *)lastId
                      herzNum:(int)herzNum
                          lat:(NSNumber *_Nullable)lat
                          lon:(NSNumber *_Nullable)lon
                     location:(NSString *_Nullable)location
                      success:(success)_success
                      failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
