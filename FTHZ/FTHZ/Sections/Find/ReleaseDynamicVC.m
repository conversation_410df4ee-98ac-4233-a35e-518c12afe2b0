#import "ReleaseDynamicVC.h"
#import "CertificateModel.h"
#import "ContentModel.h"
#import "SearchFriendsModel.h"
#import "UIImage+Wechat.h"
#import "pageTageCollectionViewCell.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import <QiniuSDK.h>
#import <objc/runtime.h>

#import "AffairTagmodel.h"
#import "FTHZInputMusicURLDialogController.h"
#import "FTHZLocationManager.h"
#import "FTHZMusicPlayerView.h"
#import "FriendSelectionVC.h"
#import "MusicInfoModel.h"
#import "MyAttentionModel.h"

#import "FZKeyedArchiver.h"
#import "_2hz-Swift.h"

// @用户位置信息模型
@interface AtUserInfo : NSObject
@property(nonatomic, strong) NSString *userid;
@property(nonatomic, strong) NSString *nickname;
@property(nonatomic, assign) NSRange textRange; // @用户名在文本中的位置范围
@end

@implementation AtUserInfo
@end

#define AffpageTageCollectionViewCell @"AffpageTageCollectionViewCell"
#define VIEW_HEIGHT(view) ((view).frame.size.height)
#define VIEW_WIDTH(view) ((view).frame.size.width)
#define VIEW_Y(view) ((view).frame.origin.y)
static CGFloat keyboardAnimationDuration = 0.5;
static const CGFloat kPhotoViewMargin = 12.0;

#define kAtUserTextColor KColor_HighBlue

@interface ReleaseDynamicVC () <UIImagePickerControllerDelegate,
                                UITextViewDelegate, UICollectionViewDataSource,
                                UICollectionViewDelegate,
                                UICollectionViewDelegateFlowLayout> {
  UICollectionView *pageTagCollectionView;
  BOOL tagSend;
}

@property(strong, nonatomic) NSMutableArray *selectedImages;
@property(strong, nonatomic) NSMutableArray *selectedAssets;
@property(strong, nonatomic) UIView *photoContainerView;

@property(strong, nonatomic) UIScrollView *scrollView;
@property(strong, nonatomic) UIButton *bottomView;
@property(assign, nonatomic) BOOL needDeleteItem;
@property(assign, nonatomic) BOOL showHud;
@property(nonatomic, strong) UITextView *dyTextView;
@property(nonatomic, strong) NSString *imageToken;
@property(nonatomic, strong) NSMutableArray *imageArray;
@property(nonatomic, assign) NSInteger imageNum;
@property(nonatomic, strong) NSMutableArray *tempUpImgArray;
@property(nonatomic, strong) NSString *videoToken;
@property(nonatomic, strong) NSString *videoKey;
@property(nonatomic, strong) UIImage *videoCover;
@property(nonatomic, assign) BOOL isPosting;
@property(nonatomic, strong) UIView *AffTagView;
@property(nonatomic, strong) NSMutableArray *affTagArray;
@property(nonatomic, strong) UIView *chouseAffTagView;
@property(nonatomic, strong) UIImageView *willChouseImageView;
@property(nonatomic, strong) UIImageView *isChouseImageView;
@property(nonatomic, strong) UIButton *willChouseBtn;
@property(nonatomic, strong) UILabel *centerUNChouseLabel;
@property(nonatomic, strong) UIButton *leftChouseBtn;
@property(nonatomic, strong) UILabel *centerChouseLabel;
@property(nonatomic, strong) UILabel *centerChouseBlackLabel;
@property(nonatomic, strong) UILabel *rightChouseLabel;
@property(nonatomic, strong) FlatButton *rightChouseBtn;
@property(nonatomic, strong) FTHZMusicPlayerEditView *playerView;
@property(nonatomic, strong) MusicInfoData *musicData;
@property(nonatomic, strong) NSString *originMusicText;
@property(nonatomic, strong) CLLocation *latestLocation;
@property(nonatomic, assign) BOOL locationHasBeenArrived;
@property(nonatomic, strong) void (^watingLocationAction)(void);
@property(nonatomic, assign) BOOL enterbackgroundSaved;
@property(nonatomic, assign) BOOL isSending;
@property(nonatomic, assign) NSInteger choosedTag;
@property(nonatomic, strong)
    NSMutableArray *taggedFriends; // 保持兼容性，用于发布时的userid数组
@property(nonatomic, strong)
    NSMutableArray<AtUserInfo *> *atUserInfos; // 新的位置信息数组
@property(nonatomic, assign) NSRange atSymbolRange;
@property(nonatomic, strong) FlatButton *timerButton;
@property(nonatomic, strong) NSDate *selectedScheduleTime;

@property(nonatomic, strong) NSMutableArray *yearArray;
@property(nonatomic, strong) NSMutableArray *monthArray;
@property(nonatomic, strong) NSMutableArray *dayArray;
@property(nonatomic, strong) NSMutableArray *hourArray;
@property(nonatomic, strong) NSMutableArray *gifDataArray;

@end

@implementation ReleaseDynamicVC

- (NSMutableArray *)selectedImages {
  if (!_selectedImages) {
    _selectedImages = [NSMutableArray array];
  }
  return _selectedImages;
}

- (NSMutableArray *)selectedAssets {
  if (!_selectedAssets) {
    _selectedAssets = [NSMutableArray array];
  }
  return _selectedAssets;
}

- (NSMutableArray *)gifDataArray {
  if (!_gifDataArray) {
    _gifDataArray = [NSMutableArray array];
  }
  return _gifDataArray;
}

- (void)loadQNtoken {
  @weakify(self);
  BOOL hasVideo = NO;
  for (PHAsset *asset in self.selectedAssets) {
    if (asset.mediaType == PHAssetMediaTypeVideo) {
      hasVideo = YES;
      break;
    }
  }

  if (hasVideo) {
    [CertificateModel getCertificateModel:@"6"
        image_name:@""
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          CertificateModel *member =
              [CertificateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            CertificateModelResult *da = [CertificateModelResult
                mj_objectWithKeyValues:[member.data objectAtIndex:0]];
            self.videoToken = da.token;
            [self uploadVideo];
          } else {
            self.isSending = NO;
            [HUD dissmiss];
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          self.isSending = NO;
          [HUD dissmiss];
          [self.view makeToast:@"数据有误,请检查网络后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }];
  } else if (_imageNum == 0) {
    [self loadPostDy];
  } else {
    [CertificateModel getCertificateModel:@"2"
        image_name:@""
        success:^(NSDictionary *resultObject) {
          @strongify(self);
          CertificateModel *member =
              [CertificateModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            CertificateModelResult *da = [CertificateModelResult
                mj_objectWithKeyValues:[member.data objectAtIndex:0]];
            self.imageToken = da.token;
            [self upData];
          } else {
            self.isSending = NO;
            [HUD dissmiss];
            [self.view makeToast:member.msg
                        duration:1.0
                        position:CSToastPositionCenter];
          }
        }
        failure:^(NSError *requestErr) {
          self.isSending = NO;
          [HUD dissmiss];
          [self.view makeToast:@"数据有误,请检查网络后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }];
  }
}

- (void)uploadVideo {
  if (self.selectedAssets.count == 0) {
    self.isSending = NO;
    [HUD dissmiss];
    [self.view makeToast:@"视频数据获取失败"
                duration:1.0
                position:CSToastPositionCenter];
    return;
  }

  PHAsset *videoAsset = self.selectedAssets.firstObject;
  if (videoAsset.mediaType != PHAssetMediaTypeVideo) {
    self.isSending = NO;
    [HUD dissmiss];
    [self.view makeToast:@"选择的不是视频文件"
                duration:1.0
                position:CSToastPositionCenter];
    return;
  }

  PHVideoRequestOptions *options = [[PHVideoRequestOptions alloc] init];
  options.version = PHVideoRequestOptionsVersionOriginal;
  options.deliveryMode = PHVideoRequestOptionsDeliveryModeHighQualityFormat;
  options.networkAccessAllowed = YES;

  [[PHImageManager defaultManager]
      requestAVAssetForVideo:videoAsset
                     options:options
               resultHandler:^(AVAsset *_Nullable asset,
                               AVAudioMix *_Nullable audioMix,
                               NSDictionary *_Nullable info) {
                 if ([asset isKindOfClass:[AVURLAsset class]]) {
                   AVURLAsset *urlAsset = (AVURLAsset *)asset;
                   NSURL *videoURL = urlAsset.URL;

                   self.videoCover = [self getVideoFirstFrame:videoURL];
                   if (!self.videoCover) {
                     dispatch_async(dispatch_get_main_queue(), ^{
                       self.isSending = NO;
                       [HUD dissmiss];
                       [self.view makeToast:@"获取视频封面失败"
                                   duration:1.0
                                   position:CSToastPositionCenter];
                     });
                     return;
                   }

                   dispatch_async(dispatch_get_main_queue(), ^{
                     QNConfiguration *config = [QNConfiguration
                         build:^(QNConfigurationBuilder *builder) {
                           builder.zone = [QNFixedZone zone0];
                           builder.useHttps = YES;
                         }];
                     QNUploadManager *upManager =
                         [[QNUploadManager alloc] initWithConfiguration:config];

                     NSString *videoKey = [NSString
                         stringWithFormat:@"video_%@%@%@",
                                          [NemoUtil randomString],
                                          CurrentUser.userid,
                                          [NemoUtil getNowTimeIntervalStr]];

                     QNUploadOption *option = [[QNUploadOption alloc]
                               initWithMime:nil
                            progressHandler:^(NSString *key, float percent) {
                              dispatch_async(dispatch_get_main_queue(), ^{
                                [self.view
                                    makeToast:[NSString
                                                  stringWithFormat:
                                                      @"视频上传中 %.0f%%",
                                                      percent * 100]
                                     duration:0.5
                                     position:CSToastPositionCenter];
                              });
                            }
                                     params:nil
                                   checkCrc:NO
                         cancellationSignal:nil];

                     [HUD show];

                     NSString *filePath = [self saveVideoToTempFile:videoURL];
                     if (!filePath) {
                       self.isSending = NO;
                       [HUD dissmiss];
                       [self.view makeToast:@"视频文件保存失败"
                                   duration:1.0
                                   position:CSToastPositionCenter];
                       return;
                     }

                     [upManager
                          putFile:filePath
                              key:videoKey
                            token:self.videoToken
                         complete:^(QNResponseInfo *info, NSString *key,
                                    NSDictionary *resp) {
                           dispatch_async(dispatch_get_main_queue(), ^{
                             if (info.ok) {
                               self.videoKey = key;
                               self.isSending = NO;
                               [self uploadVideoCover];
                             } else {
                               self.isSending = NO;
                               [HUD dissmiss];
                               [self.view
                                   makeToast:[NSString
                                                 stringWithFormat:
                                                     @"视频上传失败: %@",
                                                     info.error
                                                         .localizedDescription]
                                    duration:2.0
                                    position:CSToastPositionCenter];
                             }
                           });
                         }
                           option:option];
                   });
                 } else {
                   dispatch_async(dispatch_get_main_queue(), ^{
                     self.isSending = NO;
                     [HUD dissmiss];
                     [self.view makeToast:@"获取视频URL失败"
                                 duration:1.0
                                 position:CSToastPositionCenter];
                   });
                 }
               }];
}

- (NSString *)saveVideoToTempFile:(NSURL *)videoURL {
  NSString *tempDir = NSTemporaryDirectory();
  NSString *tempFilePath = [tempDir
      stringByAppendingPathComponent:[NSString
                                         stringWithFormat:@"%@.mp4",
                                                          [NSUUID UUID]
                                                              .UUIDString]];

  NSError *error;
  [[NSFileManager defaultManager]
      copyItemAtURL:videoURL
              toURL:[NSURL fileURLWithPath:tempFilePath]
              error:&error];

  if (error) {
    NSLog(@"保存视频到临时文件失败: %@", error.localizedDescription);
    return nil;
  }

  return tempFilePath;
}

- (void)uploadVideoCover {
  [CertificateModel getCertificateModel:@"2"
      image_name:@""
      success:^(NSDictionary *resultObject) {
        CertificateModel *member =
            [CertificateModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          CertificateModelResult *da = [CertificateModelResult
              mj_objectWithKeyValues:[member.data objectAtIndex:0]];
          self.imageToken = da.token;
          if (!self.tempUpImgArray) {
            self.tempUpImgArray = [NSMutableArray array];
          }
          [self.tempUpImgArray removeAllObjects];
          [self.tempUpImgArray addObject:@"_"];

          self.imageNum = 1;
          [self uploadImages:self.videoCover imgIndex:0];
          self.isSending = NO;
        } else {
          self.isSending = NO;
          [HUD dissmiss];
          [self.view makeToast:member.msg
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
      failure:^(NSError *requestErr) {
        self.isSending = NO;
        [HUD dissmiss];
        [self.view makeToast:@"数据有误,请检查网络后重试"
                    duration:1.0
                    position:CSToastPositionCenter];
      }];
}

- (UIImage *)getVideoFirstFrame:(NSURL *)videoURL {
  AVURLAsset *asset = [[AVURLAsset alloc] initWithURL:videoURL options:nil];
  AVAssetImageGenerator *generator =
      [[AVAssetImageGenerator alloc] initWithAsset:asset];
  generator.appliesPreferredTrackTransform = YES;
  generator.maximumSize = CGSizeMake(800, 800);

  NSError *error = nil;
  CMTime time = CMTimeMake(1, 1);
  CMTime actualTime;
  CGImageRef image = [generator copyCGImageAtTime:time
                                       actualTime:&actualTime
                                            error:&error];

  if (error) {
    return nil;
  }

  if (!image) {
    return nil;
  }

  UIImage *videoImage = [[UIImage alloc] initWithCGImage:image];
  CGImageRelease(image);
  NSData *imageData = UIImageJPEGRepresentation(videoImage, 0.8);
  return [UIImage imageWithData:imageData];
}

- (void)loadPostDy {
  if (self.isPosting) {
    return;
  }
  self.isPosting = YES;
  __weak typeof(self) wSelf = self;
  NSString *updataStr = @"";
  if (wSelf.tempUpImgArray.count > 0) {
    NSMutableArray *validImages = [NSMutableArray array];
    for (NSString *imgKey in wSelf.tempUpImgArray) {
      if (![imgKey isEqualToString:@"_"]) {
        [validImages addObject:imgKey];
      }
    }
    updataStr = [NemoUtil arrayOfStrChangeString:validImages];
  }
  NSString *Imtepy = @"";
  BOOL hasVideo = NO;
  for (PHAsset *asset in self.selectedAssets) {
    if (asset.mediaType == PHAssetMediaTypeVideo) {
      hasVideo = YES;
      break;
    }
  }

  if (hasVideo) {
    if (self.videoCover) {
      CGFloat pw = self.videoCover.size.width;
      CGFloat ph = self.videoCover.size.height;
      if (pw == ph) {
        Imtepy = @"2";
      } else if (pw > ph) {
        Imtepy = @"1";
      } else if (pw < ph) {
        Imtepy = @"3";
      }
    }
  } else if (_imageArray.count == 1) {
    id tempSendImage = [_imageArray objectAtIndex:0];
    if ([tempSendImage isKindOfClass:[UIImage class]]) {
      CGFloat pw = ((UIImage *)tempSendImage).size.width;
      CGFloat ph = ((UIImage *)tempSendImage).size.height;
      if (pw == ph) {
        Imtepy = @"2";
      } else if (pw > ph) {
        Imtepy = @"1";
      } else if (pw < ph) {
        Imtepy = @"3";
      }
    } else if (tempSendImage == [NSNull null] && _gifDataArray.count == 1) {
      NSData *gifData = [_gifDataArray objectAtIndex:0];
      UIImage *firstFrame = [UIImage imageWithData:gifData];
      if (firstFrame) {
        CGFloat pw = firstFrame.size.width;
        CGFloat ph = firstFrame.size.height;
        if (pw == ph) {
          Imtepy = @"2";
        } else if (pw > ph) {
          Imtepy = @"1";
        } else if (pw < ph) {
          Imtepy = @"3";
        }
      }
    }
  }
  NSString *tagStr = @"";
  if (_choosedTag != 999 && tagSend == YES) {
    AffairTagmodelResult *dy = [self.affTagArray objectAtIndex:_choosedTag];
    tagStr = dy.tagid;
  }

  NSNumber *timingTimestamp = nil;
  if (self.selectedScheduleTime) {
    timingTimestamp =
        @((NSInteger)[self.selectedScheduleTime timeIntervalSince1970]);
  }

  @weakify(self);
  void (^publishPost)(NSString *_Nullable, NSNumber *_Nullable,
                      NSNumber *_Nullable) =
      ^(NSString *theLocation, NSNumber *_Nullable lat,
        NSNumber *_Nullable lon) {
        @strongify(self);
        NSString *contentTxt = self.dyTextView.text;
        if (contentTxt.length == 0 && self.originMusicText.length > 0 &&
            updataStr.length == 0) {
          contentTxt = @"分享音乐";
        }
        NSMutableArray *taggedUserIds = [NSMutableArray array];
        for (AttentionUserResult *friend in self.taggedFriends) {
          [taggedUserIds addObject:friend.userid];
        }
        NSString *atUserIds = [taggedUserIds componentsJoinedByString:@","];

        [ContentModel postContentModel:contentTxt
            images:updataStr
            imageType:Imtepy
            tag:tagStr
            music:self.originMusicText
            video:self.videoKey
            lat:lat
            lon:lon
            location:theLocation
            atUserIds:atUserIds
            timing:timingTimestamp
            success:^(NSDictionary *resultObject) {
              ContentModel *member =
                  [ContentModel mj_objectWithKeyValues:resultObject];
              if ([member.success boolValue]) {
                [self sussessdiss];
              } else {
                [self.view makeToast:member.msg
                            duration:1.0
                            position:CSToastPositionCenter];
              }
              self.isSending = NO;
              self.isPosting = NO;

              [HUD dissmiss];
            }
            failure:^(NSError *requestErr) {
              self.isSending = NO;
              self.isPosting = NO;

              [HUD dissmiss];
              [self.view makeToast:@"数据有误,请检查网络后重试"
                          duration:2.0
                          position:CSToastPositionCenter];
            }];
      };

  void (^publishAction)(void) = ^{
    CLLocation *location = self.latestLocation;
    [LocationManager
        endUpdateLocationWithIdentifier:NSStringFromClass([self class])];
    if (location) {
      [LocationManager
          reverseGeocodeLocation:location
               completionHandler:^(NSArray<CLPlacemark *> *_Nullable placemarks,
                                   NSError *_Nullable error) {
                 CLPlacemark *place = placemarks.firstObject;
                 if (place) {
                   if ([place.ISOcountryCode isEqualToString:@"CN"]) {
                     publishPost(place.locality,
                                 @(place.location.coordinate.latitude),
                                 @(place.location.coordinate.longitude));
                   } else {
                     publishPost(place.country,
                                 @(place.location.coordinate.latitude),
                                 @(place.location.coordinate.longitude));
                   }
                 } else {
                   publishPost(nil, nil, nil);
                 }
               }];
    } else {
      publishPost(nil, nil, nil);
    }
  };
  if (self.locationHasBeenArrived) {
    publishAction();
  } else {
    self.watingLocationAction = [publishAction copy];
  }
}

- (void)loadTagData {
  __weak typeof(self) wSelf = self;
  if (!wSelf.affTagArray) {
    wSelf.affTagArray = [NSMutableArray new];
  } else {
    [wSelf.affTagArray removeAllObjects];
  }
  [AffairTagmodel
      getAffairTagmodel:^(NSDictionary *resultObject) {
        AffairTagmodel *member =
            [AffairTagmodel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          for (int i = 0; i < member.data.count; i++) {
            AffairTagmodelResult *dy = [AffairTagmodelResult
                mj_objectWithKeyValues:[member.data objectAtIndex:i]];
            if ([wSelf.preTagId isEqualToString:dy.tagid]) {
              wSelf.choosedTag = i;
            }
            [wSelf.affTagArray addObject:dy];
          }
          [wSelf loadAffTagView];
        }
      }
                failure:^(NSError *requestErr){

                }];
}

- (UIButton *)bottomView {
  if (!_bottomView) {
    _bottomView = [UIButton buttonWithType:UIButtonTypeCustom];
    [_bottomView setTitle:@"删除" forState:UIControlStateNormal];
    [_bottomView setTitleColor:[UIColor whiteColor]
                      forState:UIControlStateNormal];
    [_bottomView setBackgroundColor:[UIColor redColor]];
    _bottomView.frame = CGRectMake(0, self.view.frame.size.height - 50,
                                   self.view.frame.size.width, 50);
    _bottomView.alpha = 0;
  }
  return _bottomView;
}

- (void)imagePickerController:(UIImagePickerController *)picker
    didFinishPickingMediaWithInfo:(NSDictionary<NSString *, id> *)info {
  [picker dismissViewControllerAnimated:YES completion:nil];
  [AppConfig statusbarStyle:YES];

  NSString *mediaType = [info objectForKey:UIImagePickerControllerMediaType];
  if ([mediaType isEqualToString:(NSString *)kUTTypeImage]) {
    NSURL *imageURL = [info objectForKey:@"UIImagePickerControllerImageURL"];
    if (imageURL &&
        [[imageURL.pathExtension lowercaseString] isEqualToString:@"gif"]) {
      NSData *gifData = [NSData dataWithContentsOfURL:imageURL];
      if (gifData) {
        [self.imageArray addObject:[NSNull null]];
        [self.gifDataArray addObject:gifData];
        UIImage *firstFrame = [UIImage imageWithData:gifData];
        [self.selectedImages
            addObject:firstFrame ?: [UIImage imageNamed:@"gif_placeholder"]];
        self.imageNum = self.selectedImages.count;
        [self updatePhotoContainerView];
        [self checkMusicAndPhotoStatus];
        return;
      }
    }
    UIImage *image = [info objectForKey:UIImagePickerControllerOriginalImage];
    [self.imageArray addObject:image];
    [self.gifDataArray addObject:[NSNull null]];
    [self.selectedImages addObject:image];
    self.imageNum = self.selectedImages.count;
    [self updatePhotoContainerView];
    [self checkMusicAndPhotoStatus];
  } else if ([mediaType isEqualToString:(NSString *)kUTTypeMovie]) {
    NSURL *url = info[UIImagePickerControllerMediaURL];
    __block PHObjectPlaceholder *placeholder;
    [[PHPhotoLibrary sharedPhotoLibrary]
        performChanges:^{
          PHAssetChangeRequest *createAssetRequest = [PHAssetChangeRequest
              creationRequestForAssetFromVideoAtFileURL:url];
          placeholder = [createAssetRequest placeholderForCreatedAsset];
        }
        completionHandler:^(BOOL success, NSError *error) {
          if (success) {
            dispatch_async(dispatch_get_main_queue(), ^{
              PHAsset *asset = [PHAsset fetchAssetsWithLocalIdentifiers:@[
                                 placeholder.localIdentifier
                               ]
                                                                options:nil]
                                   .firstObject;
              if (asset) {
                [self.selectedAssets addObject:asset];
                PHImageRequestOptions *options =
                    [[PHImageRequestOptions alloc] init];
                options.synchronous = NO;
                options.deliveryMode =
                    PHImageRequestOptionsDeliveryModeHighQualityFormat;
                [[PHImageManager defaultManager]
                    requestImageForAsset:asset
                              targetSize:CGSizeMake(500, 500)
                             contentMode:PHImageContentModeAspectFill
                                 options:options
                           resultHandler:^(UIImage *_Nullable result,
                                           NSDictionary *_Nullable info) {
                             if (result) {
                               dispatch_async(dispatch_get_main_queue(), ^{
                                 [self.selectedImages addObject:result];
                                 [self.imageArray addObject:result];
                                 [self.gifDataArray addObject:[NSNull null]];
                                 self.imageNum = self.selectedImages.count;
                                 [self updatePhotoContainerView];
                                 [self checkMusicAndPhotoStatus];
                               });
                             }
                           }];
              }
            });
          }
        }];
  }
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
  [picker dismissViewControllerAnimated:YES completion:nil];
  [AppConfig statusbarStyle:YES];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.selectedImages = [NSMutableArray array];
  self.selectedAssets = [NSMutableArray array];
  self.imageArray = [NSMutableArray array];
  self.gifDataArray = [NSMutableArray array];
  self.imageNum = 0;
  _choosedTag = 999;
  if (@available(iOS 11.0, *)) {
    [[UIScrollView appearance] setContentInsetAdjustmentBehavior:
                                   UIScrollViewContentInsetAdjustmentNever];
  } else {
    self.automaticallyAdjustsScrollViewInsets = NO;
  }
  self.view.backgroundColor = [UIColor whiteColor];
  UIScrollView *scrollView = [[UIScrollView alloc] init];
  @weakify(self);
  if (FT_IS_IPhoneX_All) {
    scrollView.frame = CGRectMake(0, 40, kMainWidth, kMainHeight - 40);
    scrollView.alwaysBounceVertical = YES;
    [self.view addSubview:scrollView];
    self.scrollView = scrollView;

  } else {
    scrollView.frame = CGRectMake(0, 0, kMainWidth, kMainHeight);
    scrollView.alwaysBounceVertical = YES;
    [self.view addSubview:scrollView];
    self.scrollView = scrollView;
  }

  FlatButton *closeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [closeBtn setImage:KImage_name(@"Close") forState:UIControlStateNormal];
  [closeBtn addTarget:self
                action:@selector(dismissView)
      forControlEvents:UIControlEventTouchUpInside];
  [self.scrollView addSubview:closeBtn];
  [closeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.scrollView).offset(24 * kMainTemp);
    make.top.equalTo(self.scrollView).offset(22 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(20 * kMainTemp, 20 * kMainTemp));
  }];

  UILabel *headerTitle = [[UILabel alloc] init];
  headerTitle.text = @"自动保存";
  headerTitle.backgroundColor = KColor_HighBlack;
  headerTitle.textAlignment = NSTextAlignmentCenter;
  headerTitle.font = [UIFont systemFontOfSize:12 * kMainTemp];
  headerTitle.textColor = KColor_White;
  headerTitle.layer.cornerRadius = 12 * kMainTemp;
  headerTitle.layer.masksToBounds = YES;
  [self.scrollView addSubview:headerTitle];
  [headerTitle mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.centerX.equalTo(self.scrollView);
    make.top.equalTo(self.scrollView).offset(20 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(64 * kMainTemp, 24 * kMainTemp));
  }];

  FlatButton *releaseBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [releaseBtn setImage:KImage_name(@"Send") forState:UIControlStateNormal];
  [releaseBtn addTarget:self
                 action:@selector(promptView)
       forControlEvents:UIControlEventTouchUpInside];
  [self.scrollView addSubview:releaseBtn];
  [releaseBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.scrollView).offset(kMainWidth - 44 * kMainTemp);
    make.top.equalTo(self.scrollView).offset(22 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(20 * kMainTemp, 20 * kMainTemp));
  }];

  _dyTextView = [[UITextView alloc] init];
  _dyTextView.font = [UIFont systemFontOfSize:14 * kMainTemp];
  _dyTextView.toolbarPlaceholder = @"请输入你想发布的内容";
  _dyTextView.textColor = [UIColor blackColor];
  _dyTextView.text = @"";
  _dyTextView.keyboardAppearance = UIKeyboardAppearanceDark;
  _dyTextView.delegate = self;
  [self.scrollView addSubview:_dyTextView];
  [_dyTextView mas_remakeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.left.equalTo(self.scrollView).offset(16 * kMainTemp);
    make.top.equalTo(self.scrollView).offset(74);
    make.size.mas_equalTo(
        CGSizeMake(kMainWidth - 32 * kMainTemp, 180 * kMainTemp));
  }];

  if (self.hxModelArr.count > 0) {
    self.selectedAssets = [NSMutableArray arrayWithArray:self.hxModelArr];

    for (PHAsset *asset in self.hxModelArr) {
      PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
      options.synchronous = NO;
      options.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;

      [[PHImageManager defaultManager]
          requestImageForAsset:asset
                    targetSize:CGSizeMake(500, 500)
                   contentMode:PHImageContentModeAspectFill
                       options:options
                 resultHandler:^(UIImage *_Nullable result,
                                 NSDictionary *_Nullable info) {
                   if (result) {
                     [self.selectedImages addObject:result];
                     [self.imageArray addObject:result];

                     if (self.selectedImages.count == self.hxModelArr.count) {
                       dispatch_async(dispatch_get_main_queue(), ^{
                         self.imageNum = self.selectedImages.count;
                         [self updatePhotoContainerView];
                         [self checkMusicAndPhotoStatus];
                       });
                     }
                   }
                 }];
    }
  }

  CGFloat width = scrollView.frame.size.width;
  self.photoContainerView = [[UIView alloc]
      initWithFrame:CGRectMake(kPhotoViewMargin, 320 * kMainTemp,
                               width - kPhotoViewMargin * 2, 100)];
  self.photoContainerView.clipsToBounds = NO;
  [scrollView addSubview:self.photoContainerView];

  [self updatePhotoContainerView];

  _playerView = [[FTHZMusicPlayerEditView alloc] init];
  [_playerView toAddMusicStyle:NO];
  [scrollView addSubview:_playerView];
  [_playerView addTarget:self
                  action:@selector(addMusic:)
        forControlEvents:UIControlEventTouchUpInside];
  [_playerView.actionButton addTarget:self
                               action:@selector(musicActionButtonClicked:)
                     forControlEvents:UIControlEventTouchUpInside];
  [_playerView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.right.equalTo(self.view).offset(30);
    make.centerY.equalTo(self.photoContainerView);
  }];

  [self loadTagData];
  [self loadChouseTagView];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillAppear:)
             name:UIKeyboardWillShowNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(keyboardWillDisappear:)
             name:UIKeyboardWillHideNotification
           object:nil];

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(enterBackground)
             name:UIApplicationDidEnterBackgroundNotification
           object:nil];
  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(enterForeground)
             name:UIApplicationWillEnterForegroundNotification
           object:nil];

  if (CurrentUserConfig.isLocationEnabled) {
    self.locationHasBeenArrived = NO;
    [LocationManager
        beginUpdateLocationWithIdentifier:NSStringFromClass([self class])
                              updateBlock:^(CLLocation *_Nullable location,
                                            NSError *_Nullable anError) {
                                dispatch_async(dispatch_get_main_queue(), ^{
                                  @strongify(self);
                                  self.locationHasBeenArrived = YES;
                                  if (location && !anError) {
                                    self.latestLocation = location;
                                  }
                                  void (^action)(void) =
                                      self.watingLocationAction;
                                  self.watingLocationAction = nil;
                                  if (action) {
                                    action();
                                  }
                                });
                              }];
  } else {
    self.locationHasBeenArrived = YES;
  }

  if (self.draftData) {
    _dyTextView.text = self.draftData.articleContent;
    [self updateMisic:self.draftData.musicInfo
        originMusicText:self.draftData.musicOriginText];
    _choosedTag = self.draftData.selectTag;
    if (self.draftData.scheduleTime) {
      self.selectedScheduleTime = self.draftData.scheduleTime;
      NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
      [formatter setDateFormat:@"yy年M月d日HH时"];
      NSString *timeString =
          [formatter stringFromDate:self.draftData.scheduleTime];
      [self.timerButton
          setTitle:[NSString stringWithFormat:@"%@发布", timeString]
          forState:UIControlStateNormal];
      [self.timerButton setTitleColor:KColor_keyboardTxtGray
                             forState:UIControlStateNormal];
    }
  }
  self.taggedFriends = [NSMutableArray array];
  self.atUserInfos = [NSMutableArray array];
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
  [NemoUtil randomString];
  [UINavigationBar appearance].translucent = NO;
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  [AppConfig statusbarStyle:YES];

  [_dyTextView becomeFirstResponder];

  if (self.preTagId.length > 0) {
    tagSend = YES;
    self.preTagId = @"";
  }
}

- (void)openPhotoAlbum {
  [self.dyTextView resignFirstResponder];

  UIView *backgroundView = [[UIView alloc] initWithFrame:self.view.bounds];
  backgroundView.backgroundColor = [UIColor blackColor];
  backgroundView.alpha = 0;
  [self.view addSubview:backgroundView];

  UIView *mainContainerView = [[UIView alloc] init];
  [self.view addSubview:mainContainerView];

  [mainContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.view).offset(16 * kMainTemp);
    make.right.equalTo(self.view).offset(-16 * kMainTemp);
    make.bottom.equalTo(self.view).offset(200 * kMainTemp);
  }];

  UIView *actionSheetView = [[UIView alloc] init];
  actionSheetView.backgroundColor = KColor_HighBlack;
  actionSheetView.layer.cornerRadius = 20.0 * kMainTemp;
  actionSheetView.layer.masksToBounds = YES;
  [mainContainerView addSubview:actionSheetView];

  UIView *cancelContainerView = [[UIView alloc] init];
  cancelContainerView.backgroundColor = KColor_HighBlack;
  cancelContainerView.layer.cornerRadius = 20.0 * kMainTemp;
  cancelContainerView.layer.masksToBounds = YES;
  [mainContainerView addSubview:cancelContainerView];

  [actionSheetView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.left.right.equalTo(mainContainerView);
    make.height.equalTo(@(120 * kMainTemp));
  }];

  [cancelContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(actionSheetView.mas_bottom).offset(8 * kMainTemp);
    make.left.right.equalTo(mainContainerView);
    make.height.equalTo(@(60 * kMainTemp));
    make.bottom.equalTo(mainContainerView);
  }];

  UIButton *imageButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [imageButton setTitle:@"图片/GIF" forState:UIControlStateNormal];
  [imageButton setTitleColor:KColor_White forState:UIControlStateNormal];
  imageButton.titleLabel.font = [UIFont systemFontOfSize:18];
  imageButton.backgroundColor = [UIColor clearColor];
  [imageButton addTarget:self
                  action:@selector(selectImageAction)
        forControlEvents:UIControlEventTouchUpInside];
  [actionSheetView addSubview:imageButton];

  UIButton *videoButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [videoButton setTitle:@"视频" forState:UIControlStateNormal];
  [videoButton setTitleColor:KColor_White forState:UIControlStateNormal];
  videoButton.titleLabel.font = [UIFont systemFontOfSize:18];
  videoButton.backgroundColor = [UIColor clearColor];
  [videoButton addTarget:self
                  action:@selector(selectVideoAction)
        forControlEvents:UIControlEventTouchUpInside];
  [actionSheetView addSubview:videoButton];

  UIView *separatorLine = [[UIView alloc] init];
  separatorLine.backgroundColor = [UIColor colorWithWhite:0.3 alpha:1.0];
  [actionSheetView addSubview:separatorLine];

  UIButton *cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
  [cancelButton setTitle:@"取消" forState:UIControlStateNormal];
  [cancelButton setTitleColor:KColor_White forState:UIControlStateNormal];
  cancelButton.titleLabel.font = [UIFont systemFontOfSize:18];
  cancelButton.backgroundColor = [UIColor clearColor];
  [cancelButton addTarget:self
                   action:@selector(cancelAction)
         forControlEvents:UIControlEventTouchUpInside];
  [cancelContainerView addSubview:cancelButton];

  [imageButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.left.right.equalTo(actionSheetView);
    make.height.equalTo(@(60 * kMainTemp));
  }];

  [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(imageButton.mas_bottom);
    make.left.right.equalTo(actionSheetView);
    make.height.equalTo(@(0.5 * kMainTemp));
  }];

  [videoButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(separatorLine.mas_bottom);
    make.left.right.equalTo(actionSheetView);
    make.height.equalTo(@(60 * kMainTemp));
    make.bottom.equalTo(actionSheetView);
  }];

  [cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(cancelContainerView);
  }];

  UITapGestureRecognizer *tapGesture =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(cancelAction)];
  [backgroundView addGestureRecognizer:tapGesture];

  [self.view layoutIfNeeded];

  [UIView animateWithDuration:0.3
                        delay:0.0
                      options:UIViewAnimationOptionCurveEaseOut
                   animations:^{
                     backgroundView.alpha = 0.4;
                     [mainContainerView mas_updateConstraints:^(
                                            MASConstraintMaker *make) {
                       make.bottom.equalTo(self.view).offset(-34 * kMainTemp);
                     }];
                     [self.view layoutIfNeeded];
                   }
                   completion:nil];

  objc_setAssociatedObject(self, "customActionSheetBackground", backgroundView,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);
  objc_setAssociatedObject(self, "customActionSheetView", mainContainerView,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)selectImageAction {
  [self dismissCustomActionSheet];
  __weak typeof(self) weakSelf = self;
  [[TZImagePickerManager sharedManager] presentPickerWithViewController:weakSelf
      maxCount:9
      allowPickingVideo:NO
      allowPickingImage:YES
      delegate:nil
      completionHandler:^(NSArray<UIImage *> *_Nullable images,
                          NSArray<PHAsset *> *_Nullable assets,
                          BOOL isOriginal) {
        [weakSelf.imageArray removeAllObjects];
        [weakSelf.gifDataArray removeAllObjects];
        weakSelf.selectedImages = [NSMutableArray array];
        weakSelf.selectedAssets = [NSMutableArray arrayWithArray:assets];
        dispatch_group_t group = dispatch_group_create();
        for (NSInteger i = 0; i < assets.count; i++) {
          PHAsset *asset = assets[i];
          dispatch_group_enter(group);
          [[PHImageManager defaultManager]
              requestImageDataForAsset:asset
                               options:nil
                         resultHandler:^(NSData *imageData, NSString *dataUTI,
                                         UIImageOrientation orientation,
                                         NSDictionary *info) {
                           BOOL isGif =
                               [dataUTI.lowercaseString
                                   containsString:@"gif"] ||
                               (imageData.length > 0 &&
                                ((const uint8_t *)imageData.bytes)[0] == 0x47);
                           if (isGif) {
                             [weakSelf.imageArray addObject:[NSNull null]];
                             [weakSelf.gifDataArray addObject:imageData];
                             UIImage *firstFrame =
                                 [UIImage imageWithData:imageData];
                             [weakSelf.selectedImages
                                 addObject:
                                     firstFrame
                                         ?: [UIImage
                                                imageNamed:@"gif_placeholder"]];
                           } else {
                             UIImage *img = [UIImage imageWithData:imageData];
                             [weakSelf.imageArray addObject:img];
                             [weakSelf.gifDataArray addObject:[NSNull null]];
                             [weakSelf.selectedImages addObject:img];
                           }
                           dispatch_group_leave(group);
                         }];
        }
        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
          weakSelf.imageNum = weakSelf.imageArray.count;
          [weakSelf updatePhotoContainerView];
          [weakSelf checkMusicAndPhotoStatus];
        });
      }
      cancelHandler:^{
        [AppConfig statusbarStyle:YES];
      }];
}

- (void)selectVideoAction {
  [self dismissCustomActionSheet];
  __weak typeof(self) weakSelf = self;
  [[TZImagePickerManager sharedManager] presentPickerWithViewController:weakSelf
      maxCount:1
      allowPickingVideo:YES
      allowPickingImage:NO
      delegate:nil
      completionHandler:^(NSArray<UIImage *> *_Nullable images,
                          NSArray<PHAsset *> *_Nullable assets,
                          BOOL isOriginal) {
        [weakSelf.imageArray removeAllObjects];
        [weakSelf.gifDataArray removeAllObjects];
        weakSelf.selectedImages = [NSMutableArray array];
        weakSelf.selectedAssets = [NSMutableArray arrayWithArray:assets];
        dispatch_group_t group = dispatch_group_create();
        for (NSInteger i = 0; i < assets.count; i++) {
          PHAsset *asset = assets[i];
          if (asset.mediaType == PHAssetMediaTypeVideo) {
            dispatch_group_enter(group);
            PHImageRequestOptions *options =
                [[PHImageRequestOptions alloc] init];
            options.synchronous = NO;
            options.deliveryMode =
                PHImageRequestOptionsDeliveryModeHighQualityFormat;
            [[PHImageManager defaultManager]
                requestImageForAsset:asset
                          targetSize:CGSizeMake(500, 500)
                         contentMode:PHImageContentModeAspectFill
                             options:options
                       resultHandler:^(UIImage *_Nullable result,
                                       NSDictionary *_Nullable info) {
                         if (result) {
                           dispatch_async(dispatch_get_main_queue(), ^{
                             [weakSelf.selectedImages addObject:result];
                             [weakSelf.imageArray addObject:result];
                             [weakSelf.gifDataArray addObject:[NSNull null]];
                             weakSelf.imageNum = weakSelf.selectedImages.count;
                             [weakSelf updatePhotoContainerView];
                             [weakSelf checkMusicAndPhotoStatus];
                           });
                         }
                         dispatch_group_leave(group);
                       }];
          }
        }
        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
          weakSelf.imageNum = weakSelf.selectedImages.count;
          [weakSelf updatePhotoContainerView];
          [weakSelf checkMusicAndPhotoStatus];
        });
      }
      cancelHandler:^{
        [AppConfig statusbarStyle:YES];
      }];
}

- (void)cancelAction {
  [self dismissCustomActionSheet];
}

- (void)dismissCustomActionSheet {
  UIView *backgroundView =
      objc_getAssociatedObject(self, "customActionSheetBackground");
  UIView *mainContainerView =
      objc_getAssociatedObject(self, "customActionSheetView");

  [UIView animateWithDuration:0.3
      delay:0.0
      options:UIViewAnimationOptionCurveEaseIn
      animations:^{
        backgroundView.alpha = 0;
        [mainContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
          make.bottom.equalTo(self.view).offset(200);
        }];
        [self.view layoutIfNeeded];
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [mainContainerView removeFromSuperview];
        objc_setAssociatedObject(self, "customActionSheetBackground", nil,
                                 OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        objc_setAssociatedObject(self, "customActionSheetView", nil,
                                 OBJC_ASSOCIATION_RETAIN_NONATOMIC);
      }];
}

- (void)openCamera {
  [[TZImagePickerManager sharedManager]
      presentCameraWithViewController:self
                            allowCrop:NO
                             delegate:nil
                    completionHandler:^(NSArray<UIImage *> *_Nullable images,
                                        NSArray<PHAsset *> *_Nullable assets,
                                        BOOL isOriginal) {
                      if (images.count > 0) {
                        [self.selectedImages addObject:images.firstObject];
                        if (assets && assets.count > 0) {
                          [self.selectedAssets addObject:assets.firstObject];
                        }

                        [self.imageArray addObject:images.firstObject];
                        self.imageNum = self.imageArray.count;

                        [self updatePhotoContainerView];
                        [self checkMusicAndPhotoStatus];
                      }
                    }
                        cancelHandler:^{
                        }];
}

- (void)updatePhotoContainerView {
  for (UIView *subview in self.photoContainerView.subviews) {
    [subview removeFromSuperview];
  }

  CGFloat width = self.scrollView.frame.size.width - kPhotoViewMargin * 2;
  NSInteger columnCount = 3;
  CGFloat spacing = 12;
  CGFloat itemWidth = (width - spacing * (columnCount - 1)) / columnCount;
  CGFloat itemHeight = itemWidth;
  CGFloat cornerRadius = 12.0 * kMainTemp;

  for (NSInteger i = 0; i < self.selectedImages.count; i++) {
    NSInteger row = i / columnCount;
    NSInteger column = i % columnCount;

    UIView *containerView = [[UIView alloc] init];
    containerView.frame =
        CGRectMake(column * (itemWidth + spacing), row * (itemHeight + spacing),
                   itemWidth, itemHeight);
    containerView.layer.cornerRadius = cornerRadius;
    containerView.layer.masksToBounds = YES;
    [self.photoContainerView addSubview:containerView];

    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.contentMode = UIViewContentModeScaleAspectFill;
    imageView.clipsToBounds = YES;
    imageView.image = self.selectedImages[i];
    imageView.frame = CGRectMake(0, 0, itemWidth, itemHeight);
    imageView.layer.cornerRadius = cornerRadius;
    imageView.userInteractionEnabled = YES;
    [containerView addSubview:imageView];

    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self
                action:@selector(previewImage:)];
    imageView.tag = i;
    [imageView addGestureRecognizer:tapGesture];

    UIButton *deleteButton = [UIButton buttonWithType:UIButtonTypeCustom];

    UIView *deleteButtonBg =
        [[UIView alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
    deleteButtonBg.backgroundColor = [UIColor blackColor];
    deleteButtonBg.alpha = 0.5;
    deleteButtonBg.layer.cornerRadius = 12;

    CAShapeLayer *xLayer = [CAShapeLayer layer];
    UIBezierPath *path = [UIBezierPath bezierPath];

    [path moveToPoint:CGPointMake(8, 8)];
    [path addLineToPoint:CGPointMake(16, 16)];
    [path moveToPoint:CGPointMake(16, 8)];
    [path addLineToPoint:CGPointMake(8, 16)];

    xLayer.path = path.CGPath;
    xLayer.strokeColor = [UIColor whiteColor].CGColor;
    xLayer.lineWidth = 2;
    xLayer.lineCap = kCALineCapRound;

    [deleteButtonBg.layer addSublayer:xLayer];

    if (deleteButtonBg.bounds.size.width <= 0 ||
        deleteButtonBg.bounds.size.height <= 0) {
      return;
    }
    UIGraphicsBeginImageContextWithOptions(deleteButtonBg.bounds.size, NO, 0.0);
    [deleteButtonBg.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *deleteImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();

    [deleteButton setImage:deleteImage forState:UIControlStateNormal];
    deleteButton.frame = CGRectMake(itemWidth - 28, 4, 24, 24);
    deleteButton.tag = i;
    [deleteButton addTarget:self
                     action:@selector(deleteImage:)
           forControlEvents:UIControlEventTouchUpInside];
    [containerView addSubview:deleteButton];
  }

  if (self.selectedImages.count < 9) {
    NSInteger index = self.selectedImages.count;
    NSInteger row = index / columnCount;
    NSInteger column = index % columnCount;

    UIButton *addButton = [UIButton buttonWithType:UIButtonTypeCustom];
    if (self.selectedImages.count > 0) {
      [addButton setImage:KImage_name(@"添加照片")
                 forState:UIControlStateNormal];
      addButton.imageView.contentMode = UIViewContentModeCenter;
    } else {
      UIImage *cameraImage = KImage_name(@"相机");
      UIImageView *cameraImageView =
          [[UIImageView alloc] initWithImage:cameraImage];
      cameraImageView.contentMode = UIViewContentModeScaleAspectFit;
      CGFloat iconSize = itemWidth;
      cameraImageView.frame =
          CGRectMake((itemWidth - iconSize) / 2, (itemHeight - iconSize) / 2,
                     iconSize, iconSize);
      [addButton addSubview:cameraImageView];
    }

    addButton.frame =
        CGRectMake(column * (itemWidth + spacing), row * (itemHeight + spacing),
                   itemWidth, itemHeight);
    addButton.layer.borderWidth = 0.0;
    addButton.backgroundColor = [UIColor clearColor];
    [addButton addTarget:self
                  action:@selector(openPhotoAlbum)
        forControlEvents:UIControlEventTouchUpInside];
    [self.photoContainerView addSubview:addButton];
  }

  NSInteger totalRows =
      (self.selectedImages.count + (self.selectedImages.count < 9 ? 1 : 0) +
       columnCount - 1) /
      columnCount;
  CGFloat containerHeight =
      MAX(1, totalRows) * itemHeight + (MAX(1, totalRows) - 1) * spacing;

  self.photoContainerView.frame =
      CGRectMake(kPhotoViewMargin, 320 * kMainTemp, width, containerHeight);

  CGFloat bottomMargin = 20.0;
  self.scrollView.contentSize =
      CGSizeMake(self.scrollView.frame.size.width,
                 CGRectGetMaxY(self.photoContainerView.frame) + bottomMargin);
}
- (void)previewImage:(UITapGestureRecognizer *)gesture {
}

- (void)deleteImage:(UIButton *)sender {
  NSInteger index = sender.tag;
  if (index < self.selectedImages.count) {
    [self.selectedImages removeObjectAtIndex:index];
    if (index < self.selectedAssets.count) {
      [self.selectedAssets removeObjectAtIndex:index];
    }
    if (index < self.imageArray.count) {
      [self.imageArray removeObjectAtIndex:index];
    }

    self.imageNum = self.selectedImages.count;
    [self updatePhotoContainerView];
    [self checkMusicAndPhotoStatus];
  }
}

- (void)locationAuthorizationChanged:(NSNotification *)notification {
  CLAuthorizationStatus status = [notification.userInfo[@"status"] intValue];

  if (status == kCLAuthorizationStatusAuthorizedWhenInUse ||
      status == kCLAuthorizationStatusAuthorizedAlways) {
    @weakify(self);
    [LocationManager
        beginUpdateLocationWithIdentifier:NSStringFromClass([self class])
                              updateBlock:^(CLLocation *_Nullable location,
                                            NSError *_Nullable anError) {
                                dispatch_async(dispatch_get_main_queue(), ^{
                                  @strongify(self);
                                  self.locationHasBeenArrived = YES;
                                  if (location && !anError) {
                                    self.latestLocation = location;
                                  }
                                  void (^action)(void) =
                                      self.watingLocationAction;
                                  self.watingLocationAction = nil;
                                  if (action) {
                                    action();
                                  }
                                });
                              }];
  } else {
    self.locationHasBeenArrived = YES;
  }

  [[NSNotificationCenter defaultCenter]
      removeObserver:self
                name:@"LocationAuthorizationChanged"
              object:nil];
}

#pragma mark - 监听键盘
- (void)keyboardWillAppear:(NSNotification *)noti {
  if (_choosedTag == 999) {
    self.willChouseImageView.hidden = NO;
    self.willChouseBtn.hidden = NO;
    self.centerUNChouseLabel.hidden = NO;
    self.isChouseImageView.hidden = YES;
    self.leftChouseBtn.hidden = YES;
    self.centerChouseLabel.hidden = YES;
    self.centerChouseBlackLabel.hidden = YES;
  } else {
    self.willChouseImageView.hidden = YES;
    self.willChouseBtn.hidden = YES;
    self.centerUNChouseLabel.hidden = YES;
    self.isChouseImageView.hidden = NO;
    self.leftChouseBtn.hidden = NO;
    self.centerChouseLabel.hidden = NO;
    self.centerChouseBlackLabel.hidden = NO;

    AffairTagmodelResult *dy = [self.affTagArray objectAtIndex:_choosedTag];
    CGSize tp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:10 * kMainTemp]
                             width:252 * kMainTemp
                         heightMax:100 * kMainTemp
                           content:dy.name];
    self.centerChouseBlackLabel.text = dy.name;
    [self.centerChouseBlackLabel mas_remakeConstraints:^(
                                     MASConstraintMaker *make) {
      make.centerY.equalTo(self.chouseAffTagView);
      make.left.equalTo(self.centerChouseLabel.mas_right).offset(5 * kMainTemp);
      make.size.mas_equalTo(
          CGSizeMake(tp.width + 10 * kMainTemp, tp.height + 4 * kMainTemp));
    }];
  }

  NSDictionary *info = [noti userInfo];
  NSValue *value = [info objectForKey:UIKeyboardFrameEndUserInfoKey];
  keyboardAnimationDuration =
      [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
  CGSize keyboardSize = [value CGRectValue].size;
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.chouseAffTagView.frame;
                     frame.origin.y =
                         kMainHeight - keyboardSize.height - frame.size.height;
                     self.chouseAffTagView.frame = frame;
                   }];

  [UIView animateWithDuration:0.5
                   animations:^{
                     self.AffTagView.frame = CGRectMake(
                         0, kMainHeight, kMainWidth, 254 * kMainTemp);
                   }
                   completion:^(BOOL finished){
                   }];
}
- (void)keyboardWillDisappear:(NSNotification *)noti {
  [UIView animateWithDuration:keyboardAnimationDuration
                   animations:^{
                     CGRect frame = self.chouseAffTagView.frame;
                     frame.origin.y = kMainHeight;
                     self.chouseAffTagView.frame = frame;
                   }
                   completion:^(BOOL finished){
                   }];
}

- (void)loadChouseTagView {
  self.chouseAffTagView = [[UIView alloc]
      initWithFrame:CGRectMake(0, kMainHeight, kMainWidth, 38 * kMainTemp)];
  self.chouseAffTagView.backgroundColor = KColor_DarkKeyboard;
  [self.view addSubview:self.chouseAffTagView];
  self.willChouseImageView = [[UIImageView alloc] init];
  self.willChouseImageView.image = KImage_name(@"Unselected");
  [self.chouseAffTagView addSubview:self.willChouseImageView];
  [self.willChouseImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.chouseAffTagView).offset(24 * kMainTemp);
    make.centerY.equalTo(self.chouseAffTagView).offset(1 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(6 * kMainTemp, 6 * kMainTemp));
  }];
  self.willChouseBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [self.willChouseBtn addTarget:self
                         action:@selector(beganChouseTag)
               forControlEvents:UIControlEventTouchUpInside];
  [self.chouseAffTagView addSubview:self.willChouseBtn];
  [self.willChouseBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(self.chouseAffTagView);
    make.size.mas_equalTo(CGSizeMake(kMainWidth, 38 * kMainTemp));
  }];
  self.centerUNChouseLabel = [[UILabel alloc] init];
  self.centerUNChouseLabel.textColor = KColor_keyboardTxtGray;
  self.centerUNChouseLabel.text = @"同时发布到主题海域";
  self.centerUNChouseLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
  [self.chouseAffTagView addSubview:self.centerUNChouseLabel];
  [self.centerUNChouseLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.chouseAffTagView);
    make.left.equalTo(self.willChouseImageView.mas_right)
        .offset(10 * kMainTemp);
    make.height.equalTo(@(38 * kMainTemp));
  }];
  tagSend = NO;
  self.isChouseImageView = [[UIImageView alloc] init];
  self.isChouseImageView.image = KImage_name(@"Sselected");
  [self.chouseAffTagView addSubview:self.isChouseImageView];
  [self.isChouseImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.chouseAffTagView).offset(24 * kMainTemp);
    make.centerY.equalTo(self.chouseAffTagView).offset(1 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(6 * kMainTemp, 6 * kMainTemp));
  }];
  self.leftChouseBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  [self.leftChouseBtn addTarget:self
                         action:@selector(changeTagType)
               forControlEvents:UIControlEventTouchUpInside];
  [self.chouseAffTagView addSubview:self.leftChouseBtn];
  [self.leftChouseBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.chouseAffTagView);
    make.left.equalTo(self.chouseAffTagView);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 38 * kMainTemp));
  }];
  self.centerChouseLabel = [[UILabel alloc] init];
  self.centerChouseLabel.textColor = KColor_Gray;
  self.centerChouseLabel.font = [UIFont systemFontOfSize:14 * kMainTemp];
  [self.chouseAffTagView addSubview:self.centerChouseLabel];
  [self.centerChouseLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.chouseAffTagView);
    make.left.equalTo(self.willChouseImageView.mas_right)
        .offset(10 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(10 * kMainTemp, 38 * kMainTemp));
  }];
  self.centerChouseBlackLabel = [[UILabel alloc] init];
  self.centerChouseBlackLabel.textColor = KColor_HighBlack;
  self.centerChouseBlackLabel.backgroundColor = KColor_White;
  self.centerChouseBlackLabel.layer.masksToBounds = YES;
  self.centerChouseBlackLabel.layer.cornerRadius = 3;
  self.centerChouseBlackLabel.textAlignment = NSTextAlignmentCenter;
  self.centerChouseBlackLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
  [self.chouseAffTagView addSubview:self.centerChouseBlackLabel];
  [self.centerChouseBlackLabel mas_remakeConstraints:^(
                                   MASConstraintMaker *make) {
    make.centerY.equalTo(self.chouseAffTagView);
    make.left.equalTo(self.centerChouseLabel.mas_right).offset(5 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(10 * kMainTemp, 4 * kMainTemp));
  }];

  self.timerButton = [FlatButton buttonWithType:UIButtonTypeCustom];
  [self.timerButton setTitle:@"定时发布" forState:UIControlStateNormal];
  [self.timerButton setTitleColor:KColor_keyboardTxtGray
                         forState:UIControlStateNormal];
  self.timerButton.titleLabel.font = [UIFont systemFontOfSize:12 * kMainTemp];
  self.timerButton.contentHorizontalAlignment =
      UIControlContentHorizontalAlignmentRight;
  UIImage *originalImage = KImage_name(@"timingsend");
  CGSize newSize = CGSizeMake(16, 16);
  if (newSize.width <= 0 || newSize.height <= 0) {
    return;
  }
  UIGraphicsBeginImageContextWithOptions(newSize, NO, 0.0);
  [originalImage drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
  UIImage *resizedImage = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  [self.timerButton setImage:resizedImage forState:UIControlStateNormal];
  self.timerButton.imageEdgeInsets = UIEdgeInsetsMake(0, 0, 0, 5);
  self.timerButton.titleEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 0);
  [self.timerButton addTarget:self
                       action:@selector(showTimePicker)
             forControlEvents:UIControlEventTouchUpInside];
  [self.chouseAffTagView addSubview:self.timerButton];

  [self.timerButton mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(self.chouseAffTagView);
    make.right.equalTo(self.chouseAffTagView.mas_right).offset(-10 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(200 * kMainTemp, 38 * kMainTemp));
  }];
  self.willChouseImageView.hidden = YES;
  self.willChouseBtn.hidden = YES;
  self.centerUNChouseLabel.hidden = YES;
  self.isChouseImageView.hidden = YES;
  self.leftChouseBtn.hidden = YES;
  self.centerChouseLabel.hidden = YES;
  self.centerChouseBlackLabel.hidden = YES;

  self.centerChouseBlackLabel.userInteractionEnabled = YES;
  UITapGestureRecognizer *tap1 =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(beganChouseTag)];
  [self.centerChouseBlackLabel addGestureRecognizer:tap1];

  self.centerChouseLabel.userInteractionEnabled = YES;
  UITapGestureRecognizer *tap2 =
      [[UITapGestureRecognizer alloc] initWithTarget:self
                                              action:@selector(beganChouseTag)];
  [self.centerChouseLabel addGestureRecognizer:tap2];
}

- (void)showTimePicker {
  [self.dyTextView resignFirstResponder];
  dispatch_after(
      dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
      dispatch_get_main_queue(), ^{
        UIViewController *pickerVC = [[UIViewController alloc] init];
        pickerVC.modalPresentationStyle = UIModalPresentationCustom;
        UIView *containerView = [[UIView alloc] init];
        containerView.backgroundColor = KColor_DarkKeyboard;
        containerView.frame = CGRectMake(0, self.view.bounds.size.height - 280,
                                         self.view.bounds.size.width, 280);
        pickerVC.view = containerView;
        UIToolbar *toolbar = [[UIToolbar alloc] init];
        toolbar.barStyle = UIBarStyleDefault;
        toolbar.translucent = NO;
        toolbar.barTintColor = KColor_DarkKeyboard;
        toolbar.frame = CGRectMake(0, 0, containerView.frame.size.width, 44);
        UIBarButtonItem *cancelButton = [[UIBarButtonItem alloc]
            initWithTitle:@"取消"
                    style:UIBarButtonItemStylePlain
                   target:self
                   action:@selector(dismissTimePicker:)];
        [cancelButton setTitleTextAttributes:@{
          NSForegroundColorAttributeName : KColor_White
        }
                                    forState:UIControlStateNormal];

        UIBarButtonItem *doneButton =
            [[UIBarButtonItem alloc] initWithTitle:@"确定"
                                             style:UIBarButtonItemStyleDone
                                            target:self
                                            action:@selector(doneTimePicker:)];
        [doneButton setTitleTextAttributes:@{
          NSForegroundColorAttributeName : KColor_White
        }
                                  forState:UIControlStateNormal];

        UIBarButtonItem *flexibleSpace = [[UIBarButtonItem alloc]
            initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace
                                 target:nil
                                 action:nil];
        UILabel *titleLabel =
            [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 200, 44)];
        titleLabel.text = @"选择发布时间";
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.textColor = KColor_White;
        titleLabel.font = [UIFont systemFontOfSize:16];
        UIBarButtonItem *titleButton =
            [[UIBarButtonItem alloc] initWithCustomView:titleLabel];

        toolbar.items = @[
          cancelButton, flexibleSpace, titleButton, flexibleSpace, doneButton
        ];
        [containerView addSubview:toolbar];

        UIPickerView *customPicker = [[UIPickerView alloc] init];
        customPicker.delegate = self;
        customPicker.dataSource = self;
        customPicker.frame =
            CGRectMake(0, 44, containerView.frame.size.width, 236);
        customPicker.backgroundColor = KColor_DarkKeyboard;
        [containerView addSubview:customPicker];

        objc_setAssociatedObject(pickerVC, "customPicker", customPicker,
                                 OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        NSCalendar *calendar = [NSCalendar currentCalendar];
        NSDate *now = [NSDate date];
        NSDateComponents *currentComponents =
            [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth |
                                  NSCalendarUnitDay | NSCalendarUnitHour)
                        fromDate:now];

        NSInteger nextHour = currentComponents.hour + 1;
        NSDateComponents *minTimeComponents = [[NSDateComponents alloc] init];

        if (nextHour >= 24) {
          NSDate *tomorrow = [calendar dateByAddingUnit:NSCalendarUnitDay
                                                  value:1
                                                 toDate:now
                                                options:0];
          NSDateComponents *tomorrowComponents =
              [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth |
                                    NSCalendarUnitDay)
                          fromDate:tomorrow];
          minTimeComponents.year = tomorrowComponents.year;
          minTimeComponents.month = tomorrowComponents.month;
          minTimeComponents.day = tomorrowComponents.day;
          minTimeComponents.hour = 0;
        } else {
          minTimeComponents.year = currentComponents.year;
          minTimeComponents.month = currentComponents.month;
          minTimeComponents.day = currentComponents.day;
          minTimeComponents.hour = nextHour;
        }

        objc_setAssociatedObject(customPicker, "minTimeComponents",
                                 minTimeComponents,
                                 OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        self.yearArray = [NSMutableArray array];
        self.monthArray = [NSMutableArray array];
        self.dayArray = [NSMutableArray array];
        self.hourArray = [NSMutableArray array];

        for (NSInteger year = minTimeComponents.year;
             year <= minTimeComponents.year + 5; year++) {
          [self.yearArray
              addObject:[NSString stringWithFormat:@"%ld年", (long)year]];
        }

        NSInteger startMonth = minTimeComponents.month;
        for (NSInteger month = startMonth; month <= 12; month++) {
          [self.monthArray
              addObject:[NSString stringWithFormat:@"%ld月", (long)month]];
        }

        NSDateComponents *components = [[NSDateComponents alloc] init];
        components.year = minTimeComponents.year;
        components.month = minTimeComponents.month;
        NSRange range =
            [calendar rangeOfUnit:NSCalendarUnitDay
                           inUnit:NSCalendarUnitMonth
                          forDate:[calendar dateFromComponents:components]];
        NSInteger startDay = minTimeComponents.day;
        for (NSInteger day = startDay; day <= range.length; day++) {
          [self.dayArray
              addObject:[NSString stringWithFormat:@"%ld日", (long)day]];
        }

        NSInteger startHour = minTimeComponents.hour;
        for (NSInteger hour = startHour; hour < 24; hour++) {
          [self.hourArray
              addObject:[NSString stringWithFormat:@"%ld时", (long)hour]];
        }

        [customPicker selectRow:0 inComponent:0 animated:NO];
        [customPicker selectRow:0 inComponent:1 animated:NO];
        [customPicker selectRow:0 inComponent:2 animated:NO];
        [customPicker selectRow:0 inComponent:3 animated:NO];

        UIView *backgroundView =
            [[UIView alloc] initWithFrame:self.view.bounds];
        backgroundView.backgroundColor = [UIColor blackColor];
        backgroundView.alpha = 0;
        [self.view addSubview:backgroundView];

        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(dismissTimePicker:)];
        [backgroundView addGestureRecognizer:tapGesture];

        objc_setAssociatedObject(pickerVC, "backgroundView", backgroundView,
                                 OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        [self addChildViewController:pickerVC];
        [self.view addSubview:pickerVC.view];

        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        [UIView animateWithDuration:0.3
                         animations:^{
                           pickerVC.view.transform = CGAffineTransformIdentity;
                           backgroundView.alpha = 0.4;
                         }];
      });
}

- (void)updateMonthOptionsForYear:(NSInteger)yearIndex
                     inPickerView:(UIPickerView *)pickerView {
  NSDateComponents *minTimeComponents =
      objc_getAssociatedObject(pickerView, "minTimeComponents");
  [self.monthArray removeAllObjects];
  NSInteger selectedYear =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];
  NSInteger startMonth =
      (selectedYear == minTimeComponents.year) ? minTimeComponents.month : 1;
  for (NSInteger month = startMonth; month <= 12; month++) {
    [self.monthArray
        addObject:[NSString stringWithFormat:@"%ld月", (long)month]];
  }
}

- (void)updateDayOptionsForYear:(NSInteger)yearIndex
                          month:(NSInteger)month
                   inPickerView:(UIPickerView *)pickerView {
  NSDateComponents *minTimeComponents =
      objc_getAssociatedObject(pickerView, "minTimeComponents");
  [self.dayArray removeAllObjects];
  NSInteger selectedYear =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];
  NSInteger startDay = 1;
  if (selectedYear == minTimeComponents.year &&
      month == minTimeComponents.month) {
    startDay = minTimeComponents.day;
  }
  NSCalendar *calendar = [NSCalendar currentCalendar];
  NSDateComponents *components = [[NSDateComponents alloc] init];
  components.year = selectedYear;
  components.month = month;
  NSRange range =
      [calendar rangeOfUnit:NSCalendarUnitDay
                     inUnit:NSCalendarUnitMonth
                    forDate:[calendar dateFromComponents:components]];
  for (NSInteger day = startDay; day <= range.length; day++) {
    [self.dayArray addObject:[NSString stringWithFormat:@"%ld日", (long)day]];
  }
}

- (void)updateHourOptionsForYear:(NSInteger)yearIndex
                           month:(NSInteger)month
                             day:(NSInteger)day
                    inPickerView:(UIPickerView *)pickerView {
  NSDateComponents *minTimeComponents =
      objc_getAssociatedObject(pickerView, "minTimeComponents");
  [self.hourArray removeAllObjects];
  NSInteger selectedYear =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];
  NSInteger startHour = 0;
  if (selectedYear == minTimeComponents.year &&
      month == minTimeComponents.month && day == minTimeComponents.day) {
    startHour = minTimeComponents.hour;
  }
  for (NSInteger hour = startHour; hour < 24; hour++) {
    [self.hourArray addObject:[NSString stringWithFormat:@"%ld时", (long)hour]];
  }
}

#pragma mark - UIPickerViewDataSource & UIPickerViewDelegate

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
  return 4;
}

- (CGFloat)pickerView:(UIPickerView *)pickerView
    widthForComponent:(NSInteger)component {
  switch (component) {
  case 0:
    return 100;
  case 1:
    return 80;
  case 2:
    return 80;
  case 3:
    return 80;
  default:
    return 0;
  }
}

- (NSInteger)pickerView:(UIPickerView *)pickerView
    numberOfRowsInComponent:(NSInteger)component {
  switch (component) {
  case 0:
    return self.yearArray.count;
  case 1:
    return self.monthArray.count;
  case 2:
    return self.dayArray.count;
  case 3:
    return self.hourArray.count;
  default:
    return 0;
  }
}

- (UIView *)pickerView:(UIPickerView *)pickerView
            viewForRow:(NSInteger)row
          forComponent:(NSInteger)component
           reusingView:(UIView *)view {
  UILabel *label = (UILabel *)view;
  if (!label) {
    label = [[UILabel alloc] init];
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [UIFont systemFontOfSize:20];
  }

  label.textColor = KColor_White;

  switch (component) {
  case 0:
    label.text = self.yearArray[row];
    break;
  case 1:
    label.text = self.monthArray[row];
    break;
  case 2:
    label.text = self.dayArray[row];
    break;
  case 3:
    label.text = self.hourArray[row];
    break;
  default:
    break;
  }

  return label;
}

- (void)syncPicker:(UIPickerView *)pickerView
         yearIndex:(NSInteger)yearIndex
        monthIndex:(NSInteger)monthIndex
          dayIndex:(NSInteger)dayIndex
        forceToMin:(BOOL)forceToMin {
  NSDateComponents *minTimeComponents =
      objc_getAssociatedObject(pickerView, "minTimeComponents");
  NSInteger selectedYear =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];

  [self updateMonthOptionsForYear:yearIndex inPickerView:pickerView];
  [pickerView reloadComponent:1];
  NSInteger monthRow = [pickerView selectedRowInComponent:1];
  if (forceToMin && selectedYear == minTimeComponents.year) {
    for (NSInteger i = 0; i < self.monthArray.count; i++) {
      if ([[self.monthArray[i] stringByReplacingOccurrencesOfString:@"月"
                                                         withString:@""]
              integerValue] == minTimeComponents.month) {
        monthRow = i;
        break;
      }
    }
    [pickerView selectRow:monthRow inComponent:1 animated:YES];
  }

  NSInteger selectedMonth = [[self.monthArray[monthRow]
      stringByReplacingOccurrencesOfString:@"月"
                                withString:@""] integerValue];

  [self updateDayOptionsForYear:yearIndex
                          month:selectedMonth
                   inPickerView:pickerView];
  [pickerView reloadComponent:2];
  NSInteger dayRow = [pickerView selectedRowInComponent:2];
  if (forceToMin && selectedYear == minTimeComponents.year &&
      selectedMonth == minTimeComponents.month) {
    for (NSInteger i = 0; i < self.dayArray.count; i++) {
      if ([[self.dayArray[i] stringByReplacingOccurrencesOfString:@"日"
                                                       withString:@""]
              integerValue] == minTimeComponents.day) {
        dayRow = i;
        break;
      }
    }
    [pickerView selectRow:dayRow inComponent:2 animated:YES];
  }

  NSInteger selectedDay = [[self.dayArray[dayRow]
      stringByReplacingOccurrencesOfString:@"日"
                                withString:@""] integerValue];

  [self updateHourOptionsForYear:yearIndex
                           month:selectedMonth
                             day:selectedDay
                    inPickerView:pickerView];
  [pickerView reloadComponent:3];
  NSInteger hourRow = [pickerView selectedRowInComponent:3];
  if (forceToMin && selectedYear == minTimeComponents.year &&
      selectedMonth == minTimeComponents.month &&
      selectedDay == minTimeComponents.day) {
    for (NSInteger i = 0; i < self.hourArray.count; i++) {
      if ([[self.hourArray[i] stringByReplacingOccurrencesOfString:@"时"
                                                        withString:@""]
              integerValue] == minTimeComponents.hour) {
        hourRow = i;
        break;
      }
    }
    [pickerView selectRow:hourRow inComponent:3 animated:YES];
  }
}

- (void)pickerView:(UIPickerView *)pickerView
      didSelectRow:(NSInteger)row
       inComponent:(NSInteger)component {
  NSInteger yearIndex = [pickerView selectedRowInComponent:0];
  NSInteger monthIndex = [pickerView selectedRowInComponent:1];
  NSInteger dayIndex = [pickerView selectedRowInComponent:2];

  if (component == 0) {
    [self syncPicker:pickerView
           yearIndex:yearIndex
          monthIndex:0
            dayIndex:0
          forceToMin:YES];
  } else if (component == 1) {
    [self syncPicker:pickerView
           yearIndex:yearIndex
          monthIndex:monthIndex
            dayIndex:0
          forceToMin:NO];
  } else if (component == 2) {
    [self syncPicker:pickerView
           yearIndex:yearIndex
          monthIndex:monthIndex
            dayIndex:dayIndex
          forceToMin:NO];
  }
}

- (void)updateDaysForYear:(NSInteger)yearIndex month:(NSInteger)month {
  [self.dayArray removeAllObjects];

  NSCalendar *calendar = [NSCalendar currentCalendar];
  NSDateComponents *components = [[NSDateComponents alloc] init];
  components.year =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];
  components.month = month;

  NSRange range =
      [calendar rangeOfUnit:NSCalendarUnitDay
                     inUnit:NSCalendarUnitMonth
                    forDate:[calendar dateFromComponents:components]];

  for (NSInteger day = 1; day <= range.length; day++) {
    [self.dayArray addObject:[NSString stringWithFormat:@"%ld日", (long)day]];
  }
}

- (void)dismissTimePicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");
  [self.timerButton setTitle:@"定时发布" forState:UIControlStateNormal];
  [self.timerButton setTitleColor:KColor_keyboardTxtGray
                         forState:UIControlStateNormal];
  UIImage *originalImage = KImage_name(@"timingsend");
  CGSize newSize = CGSizeMake(16, 16);
  if (newSize.width <= 0 || newSize.height <= 0) {
    return;
  }
  UIGraphicsBeginImageContextWithOptions(newSize, NO, 0.0);
  [originalImage drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
  UIImage *resizedImage = UIGraphicsGetImageFromCurrentImageContext();
  UIGraphicsEndImageContext();
  [self.timerButton setImage:resizedImage forState:UIControlStateNormal];

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];

        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              [self.dyTextView becomeFirstResponder];
            });
      }];
}

- (void)doneTimePicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIPickerView *customPicker =
      objc_getAssociatedObject(pickerVC, "customPicker");

  NSInteger yearIndex = [customPicker selectedRowInComponent:0];
  NSInteger monthIndex = [customPicker selectedRowInComponent:1];
  NSInteger dayIndex = [customPicker selectedRowInComponent:2];
  NSInteger hourIndex = [customPicker selectedRowInComponent:3];

  NSInteger selectedYear =
      [[self.yearArray[yearIndex] substringToIndex:4] integerValue];
  NSInteger selectedMonth = [[self.monthArray[monthIndex]
      stringByReplacingOccurrencesOfString:@"月"
                                withString:@""] integerValue];
  NSInteger selectedDay = [[self.dayArray[dayIndex]
      stringByReplacingOccurrencesOfString:@"日"
                                withString:@""] integerValue];
  NSInteger selectedHour = [[self.hourArray[hourIndex]
      stringByReplacingOccurrencesOfString:@"时"
                                withString:@""] integerValue];

  NSCalendar *calendar = [NSCalendar currentCalendar];
  NSDateComponents *components = [[NSDateComponents alloc] init];
  components.year = selectedYear;
  components.month = selectedMonth;
  components.day = selectedDay;
  components.hour = selectedHour;
  components.minute = 0;
  components.second = 0;
  NSDate *selectedDate = [calendar dateFromComponents:components];

  NSDate *now = [NSDate date];
  NSDateComponents *nowComponents =
      [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth |
                            NSCalendarUnitDay | NSCalendarUnitHour)
                  fromDate:now];
  NSInteger nextHour = nowComponents.hour + 1;
  NSDateComponents *minTimeComponents = [[NSDateComponents alloc] init];
  if (nextHour >= 24) {
    NSDate *tomorrow = [calendar dateByAddingUnit:NSCalendarUnitDay
                                            value:1
                                           toDate:now
                                          options:0];
    NSDateComponents *tomorrowComponents =
        [calendar components:(NSCalendarUnitYear | NSCalendarUnitMonth |
                              NSCalendarUnitDay)
                    fromDate:tomorrow];
    minTimeComponents.year = tomorrowComponents.year;
    minTimeComponents.month = tomorrowComponents.month;
    minTimeComponents.day = tomorrowComponents.day;
    minTimeComponents.hour = 0;
  } else {
    minTimeComponents.year = nowComponents.year;
    minTimeComponents.month = nowComponents.month;
    minTimeComponents.day = nowComponents.day;
    minTimeComponents.hour = nextHour;
  }
  NSDate *minDate = [calendar dateFromComponents:minTimeComponents];

  if ([selectedDate compare:minDate] == NSOrderedAscending) {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yy年M月d日HH时"];
    NSString *minDateString = [formatter stringFromDate:minDate];
    [self.view makeToast:[NSString stringWithFormat:@"请选择%@及以后的时间",
                                                    minDateString]
                duration:2.0
                position:CSToastPositionCenter];
    return;
  }

  self.selectedScheduleTime = selectedDate;

  [self.timerButton setTitleColor:KColor_keyboardTxtGray
                         forState:UIControlStateNormal];

  NSString *timeString = [NSString
      stringWithFormat:@"%@%@%@%@", self.yearArray[yearIndex],
                       self.monthArray[monthIndex], self.dayArray[dayIndex],
                       self.hourArray[hourIndex]];

  [self.timerButton setTitle:[NSString stringWithFormat:@"%@发布", timeString]
                    forState:UIControlStateNormal];

  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];

        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              [self.dyTextView becomeFirstResponder];
            });
      }];
}

- (void)loadAffTagView {
  self.AffTagView = [[UIView alloc]
      initWithFrame:CGRectMake(0, kMainHeight, kMainWidth, 254 * kMainTemp)];
  self.AffTagView.backgroundColor = KColor_DarkKeyboard;
  [self.view addSubview:self.AffTagView];

  UIView *upView = [[UIView alloc] init];
  upView.backgroundColor = UIColor.clearColor;
  [self.AffTagView addSubview:upView];
  [upView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.AffTagView);
    make.size.mas_equalTo(CGSizeMake(kMainWidth, 38 * kMainTemp));
  }];

  FlatButton *cancelLabel = [FlatButton buttonWithType:UIButtonTypeCustom];
  [cancelLabel setTitle:@"取消" forState:UIControlStateNormal];
  [cancelLabel addTarget:self
                  action:@selector(tagChouseCancel)
        forControlEvents:UIControlEventTouchUpInside];
  [cancelLabel setTitleColor:KColor_White forState:UIControlStateNormal];
  cancelLabel.titleLabel.font = [UIFont systemFontOfSize:14 * kMainTemp];
  [upView addSubview:cancelLabel];
  [cancelLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(upView);
    make.left.equalTo(upView).offset(16 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(30 * kMainTemp, 38 * kMainTemp));
  }];

  UILabel *tagTittleLabel = [[UILabel alloc] init];
  tagTittleLabel.textColor = UIColor.whiteColor;
  tagTittleLabel.text = @"选择主题";
  tagTittleLabel.font = [UIFont systemFontOfSize:16 * kMainTemp];
  [upView addSubview:tagTittleLabel];
  [tagTittleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(upView);
    make.size.mas_equalTo(CGSizeMake(80 * kMainTemp, 38 * kMainTemp));
  }];

  FlatButton *saveLabel = [FlatButton buttonWithType:UIButtonTypeCustom];
  [saveLabel setTitle:@"确定" forState:UIControlStateNormal];
  [saveLabel addTarget:self
                action:@selector(tagChouseSuss)
      forControlEvents:UIControlEventTouchUpInside];
  [saveLabel setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
  saveLabel.titleLabel.font = [UIFont systemFontOfSize:14 * kMainTemp];
  [upView addSubview:saveLabel];
  [saveLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(upView);
    make.right.equalTo(upView.mas_right).offset(-16 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(30 * kMainTemp, 38 * kMainTemp));
  }];

  UICollectionViewFlowLayout *layout =
      [[UICollectionViewFlowLayout alloc] init];
  layout.itemSize = CGSizeMake(150 * kMainTemp, 35 * kMainTemp);
  layout.minimumLineSpacing = 15 * kMainTemp;
  layout.minimumInteritemSpacing = 12 * kMainTemp;
  layout.sectionInset = UIEdgeInsetsMake(12 * kMainTemp, 30 * kMainTemp,
                                         12 * kMainTemp, 30 * kMainTemp);
  layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
  pageTagCollectionView = [[UICollectionView alloc]
             initWithFrame:CGRectMake(0, 38 * kMainTemp, kMainWidth,
                                      200 * kMainTemp)
      collectionViewLayout:layout];
  pageTagCollectionView.backgroundColor = KColor_DarkKeyboard;
  pageTagCollectionView.dataSource = self;
  pageTagCollectionView.delegate = self;
  pageTagCollectionView.showsHorizontalScrollIndicator = NO;
  [pageTagCollectionView registerClass:[pageTageCollectionViewCell class]
            forCellWithReuseIdentifier:AffpageTageCollectionViewCell];
  [self.AffTagView addSubview:pageTagCollectionView];
}

#pragma mark - Music Editing

- (void)musicActionButtonClicked:(UIButton *)button {
  if (!self.musicData) {
    [self addMusic:button];
    return;
  }
  [self updateMisic:nil originMusicText:nil];
}

- (void)addMusic:(id)sender {
  if (self.musicData) {
    return;
  }
  @weakify(self);
  FZAddMusic *addMusic = [[FZAddMusic alloc] init];
  addMusic.completion = ^(NSString *url) {
    @strongify(self);
    [self userCommittedURL:url];
  };
  addMusic.modalPresentationStyle = UIModalPresentationFullScreen;
  [self presentViewController:addMusic animated:true completion:nil];
}

- (void)userCommittedURL:(NSString *)url {
  [HUD show];
  @weakify(self);
  [MusicInfoModel getMusicInfoFromURL:url
      success:^(NSDictionary *resultObject) {
        [HUD dissmiss];
        @strongify(self);
        NSError *serializeError = nil;
        MusicInfoModel *info =
            [[MusicInfoModel alloc] initWithDictionary:resultObject
                                                 error:&serializeError];
        if (serializeError) {
          [self.view makeToast:info.msg ?: @"解析歌曲信息失败，请重试"
                      duration:1.0
                      position:CSToastPositionCenter];
          return;
        }
        MusicInfoData *data = info.data;
        if ([info.code isEqualToNumber:@(0)] &&
            [data isKindOfClass:[MusicInfoData class]]) {
          [self updateMisic:data originMusicText:url];
        } else {
          [self.view makeToast:info.msg ?: @"请求歌曲信息失败，请重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        @strongify(self);
        [self.view makeToast:@"请求歌曲信息失败，请重试"
                    duration:1.0
                    position:CSToastPositionCenter];
      }];
}

- (void)updateMisic:(MusicInfoData *)data
    originMusicText:(NSString *)musicText {
  self.musicData = data;
  self.originMusicText = musicText;
  [self.playerView
      setMusicCover:data.imgUrl ? [NSURL URLWithString:data.imgUrl] : nil
               name:data.musicName
             artist:data.artists
           platform:[data.from integerValue]];
  if ([data isKindOfClass:[MusicInfoData class]]) {
    [self.playerView toDisplayMusicStyle:YES];
  } else {
    [self.playerView toAddMusicStyle:YES];
  }
  [self checkMusicAndPhotoStatus];
}

#pragma mark - 点击方法

- (void)tagChouseCancel {
  [UIView animateWithDuration:0.5
      animations:^{
        self.AffTagView.frame =
            CGRectMake(0, kMainHeight, kMainWidth, 254 * kMainTemp);
      }
      completion:^(BOOL finished) {
        NSInteger tempIndextag = 0;
        if (self.choosedTag != 999) {
          tempIndextag = self.choosedTag;
        }
        self.choosedTag = 999;
        [self->pageTagCollectionView
            reloadItemsAtIndexPaths:
                [NSArray
                    arrayWithObjects:[NSIndexPath indexPathForRow:tempIndextag
                                                        inSection:0],
                                     nil]];
        [self.dyTextView becomeFirstResponder];
      }];
}

- (void)tagChouseSuss {
  [UIView animateWithDuration:0.25
      animations:^{
        self.AffTagView.frame =
            CGRectMake(0, kMainHeight, kMainWidth, 254 * kMainTemp);
      }
      completion:^(BOOL finished) {
        [self.willChouseBtn removeTarget:self
                                  action:@selector(changeTagType)
                        forControlEvents:UIControlEventTouchUpInside];
        [self.willChouseBtn addTarget:self
                               action:@selector(beganChouseTag)
                     forControlEvents:UIControlEventTouchUpInside];

        [self.dyTextView becomeFirstResponder];
      }];
}

- (void)changeTag {
  [UIView animateWithDuration:0.5
                   animations:^{

                   }
                   completion:^(BOOL finished){
                   }];
}

- (void)beganChouseTag {
  tagSend = YES;
  [self.dyTextView resignFirstResponder];
  [UIView animateWithDuration:0.5
                   animations:^{
                     self.AffTagView.frame =
                         CGRectMake(0, kMainHeight - 254 * kMainTemp,
                                    kMainWidth, 254 * kMainTemp);
                   }
                   completion:^(BOOL finished){
                   }];
}

- (void)changeTagType {
  if (tagSend) {
    tagSend = NO;
    self.isChouseImageView.image = KImage_name(@"Unselected");
  } else {
    tagSend = YES;
    self.isChouseImageView.image = KImage_name(@"Sselected");
  }
}

#pragma mark - collectionView  data source
- (NSInteger)collectionView:(UICollectionView *)collectionView
     numberOfItemsInSection:(NSInteger)section {
  return self.affTagArray.count;
}

- (NSInteger)numberOfSectionsInCollectionView:
    (UICollectionView *)collectionView {
  return 1;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {
  AffairTagmodelResult *dy = [self.affTagArray objectAtIndex:indexPath.row];
  pageTageCollectionViewCell *cell = [collectionView
      dequeueReusableCellWithReuseIdentifier:AffpageTageCollectionViewCell
                                forIndexPath:indexPath];
  if (cell == nil) {
    cell = [[pageTageCollectionViewCell alloc] init];
  }
  if (indexPath.row == _choosedTag) {
    cell.ptagBtn.backgroundColor = KColor_White;
    cell.ptagBtn.layer.borderColor = KColor_White.CGColor;
    cell.ptagBtn.textColor = KColor_Black;
  } else {
    cell.ptagBtn.backgroundColor = KColor_DarkKeyboard;
    cell.ptagBtn.layer.borderColor = KColor_White.CGColor;
    cell.ptagBtn.textColor = KColor_White;
  }
  cell.ptagBtn.text = dy.name;
  return cell;
}

- (void)collectionView:(UICollectionView *)collectionView
    didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
  NSInteger tempIndextag = 0;
  if (_choosedTag != 999) {
    tempIndextag = _choosedTag;
  }
  _choosedTag = indexPath.row;
  [pageTagCollectionView
      reloadItemsAtIndexPaths:[NSArray
                                  arrayWithObjects:[NSIndexPath
                                                       indexPathForRow:indexPath
                                                                           .row
                                                             inSection:0],
                                                   nil]];
  [pageTagCollectionView
      reloadItemsAtIndexPaths:
          [NSArray arrayWithObjects:[NSIndexPath indexPathForRow:tempIndextag
                                                       inSection:0],
                                    nil]];
}

- (void)dealloc {
  self.videoToken = nil;
  self.videoKey = nil;
  self.videoCover = nil;
  [NSObject cancelPreviousPerformRequestsWithTarget:self];
  [LocationManager
      endUpdateLocationWithIdentifier:NSStringFromClass([self class])];
  [[NSNotificationCenter defaultCenter] removeObserver:self];
  [[NSNotificationCenter defaultCenter]
      removeObserver:self
                name:@"LocationAuthorizationChanged"
              object:nil];
}

- (void)didNavBtnClick {
  [self openPhotoAlbum];
}

- (void)checkMusicAndPhotoStatus {
  @weakify(self);
  if (_imageNum == 0 && self.musicData == nil) {
    if (self.playerView) {
      [self.playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.right.equalTo(self.view).offset(30);
        make.centerY.equalTo(self.photoContainerView);
      }];
    }
  } else {
    if (self.playerView) {
      [self.playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.left.equalTo(self.photoContainerView);
        make.top.equalTo(self.photoContainerView.mas_top)
            .offset(-80 * kMainTemp);
        make.width.equalTo(@(200 * kMainTemp));
        make.height.equalTo(@(60 * kMainTemp));
      }];
    }
  }
}

- (void)uploadImages:(UIImage *)scaledImage imgIndex:(NSInteger)imgIndex {
  @weakify(self);
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];

  NSString *imgKey = [NSString
      stringWithFormat:@"%@%@%@", [NemoUtil randomString], CurrentUser.userid,
                       [NemoUtil getNowTimeIntervalStr]];
  [upManager
       putFile:[self getImagePath:scaledImage]
           key:imgKey
         token:self.imageToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        @strongify(self);
        if (info.ok) {
          [self.tempUpImgArray replaceObjectAtIndex:imgIndex withObject:imgKey];
          self.imageNum = self.imageNum - 1;
          if (self.imageNum == 0) {
            for (NSString *tepImg in [self.tempUpImgArray copy]) {
              if ([tepImg isEqualToString:@"_"]) {
                [self.tempUpImgArray removeObject:tepImg];
              }
            }
            [self loadPostDy];
          }

        } else {
          self.isSending = NO;
          [HUD dissmiss];
          [self.view makeToast:@"发送失败,请检查网络连接后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
        option:option];
}
- (NSString *)typeForImageData:(NSData *)data {
  uint8_t c;
  [data getBytes:&c length:1];
  switch (c) {
  case 0xFF:
    return @"jpeg";
  case 0x89:
    return @"png";
  case 0x47:
    return @"gif";
  case 0x49:
  case 0x4D:
    return @"tiff";
  }
  return nil;
}

- (NSString *)getImagePath:(UIImage *)Image {
  if ((id)Image == [NSNull null])
    return nil;
  NSData *data = [Image wcTimelineCompress];
  NSString *filePath = nil;
  NSString *DocumentsPath =
      [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];
  NSFileManager *fileManager = [NSFileManager defaultManager];
  [fileManager createDirectoryAtPath:DocumentsPath
         withIntermediateDirectories:YES
                          attributes:nil
                               error:nil];
  NSString *ImagePath = [[NSString alloc] initWithFormat:@"/theFirstImage.png"];
  [fileManager
      createFileAtPath:[DocumentsPath stringByAppendingString:ImagePath]
              contents:data
            attributes:nil];
  filePath =
      [[NSString alloc] initWithFormat:@"%@%@", DocumentsPath, ImagePath];
  return filePath;
}

- (void)upData {
  if (!_tempUpImgArray) {
    _tempUpImgArray = [NSMutableArray new];
  } else {
    [_tempUpImgArray removeAllObjects];
  }
  for (int i = 0; i < _imageArray.count; i++) {
    [_tempUpImgArray addObject:@"_"];
  }
  for (int i = 0; i < _imageArray.count; i++) {
    id img = [_imageArray objectAtIndex:i];
    id gifData =
        (self.gifDataArray.count > i) ? self.gifDataArray[i] : [NSNull null];
    BOOL isGif = (gifData && gifData != [NSNull null] &&
                  [[self typeForImageData:gifData] isEqualToString:@"gif"]);
    if (isGif) {
      [self uploadGifData:gifData imgIndex:i];
    } else if (img && img != [NSNull null]) {
      [self uploadImages:img imgIndex:i];
    }
  }
}

- (void)sussessdiss {
  [self.selectedImages removeAllObjects];
  [self.selectedAssets removeAllObjects];
  [self.imageArray removeAllObjects];
  [self.gifDataArray removeAllObjects];
  self.imageNum = 0;
  [NOTIFICENTER postNotificationName:DySendSussreloadData object:nil];
  [self draftSendSuccess];
  [self dismissViewControllerAnimated:YES
                           completion:^{

                           }];
}

- (void)dismissView {
  if ([self isCanDraft]) {
    [self saveDraftBox];
  }

  [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)promptView {
  NSLog(@"🔴 点击发布按钮");
  NSLog(@"🔴 当前文本: '%@'", self.dyTextView.text);
  NSLog(@"🔴 当前atUserInfos数量: %lu", (unsigned long)self.atUserInfos.count);
  for (AtUserInfo *info in self.atUserInfos) {
    NSLog(@"🔴 AtUserInfo: %@ (ID: %@, 位置: %@)", info.nickname, info.userid,
          NSStringFromRange(info.textRange));
  }
  NSLog(@"🔴 当前taggedFriends数量: %lu",
        (unsigned long)self.taggedFriends.count);
  for (AttentionUserResult *friend in self.taggedFriends) {
    NSLog(@"🔴 TaggedFriend: %@ (ID: %@)", friend.nickname, friend.userid);
  }

  // 暂时禁用发布功能，只打印信息
  [self.view makeToast:@"调试模式：发布功能已暂时禁用"
              duration:2.0
              position:CSToastPositionCenter];
  return;

  NSString *trimmedText = [self.dyTextView.text
      stringByTrimmingCharactersInSet:[NSCharacterSet
                                          whitespaceAndNewlineCharacterSet]];
  BOOL isDefaultText =
      [self.dyTextView.text isEqualToString:@"请输入你想发布的内容"];
  BOOL hasNoContent = (_imageNum == 0 && self.originMusicText.length == 0 &&
                       trimmedText.length == 0);

  if (hasNoContent) {
    [self.view makeToast:@"请输入你想发布的内容"
                duration:1.0
                position:CSToastPositionCenter];
    return;
  }

  if (self.dyTextView.text.length > 2000) {
    [self.view makeToast:@"字数太多了 🐳"
                duration:1.0
                position:CSToastPositionCenter];
  } else {
    if (self.selectedScheduleTime) {
      NSDate *minAllowedTime = [[NSDate date] dateByAddingTimeInterval:3600];

      if ([self.selectedScheduleTime compare:minAllowedTime] ==
          NSOrderedAscending) {
        [self.view
            makeToast:
                @"定时发布时间需要至少比当前时间晚1小时，请重新选择发布时间"
             duration:2.0
             position:CSToastPositionCenter];
        return;
      }
    }
    if (isDefaultText) {
      self.dyTextView.text = @"";
    }

    [HUD show];
    if (self.isSending) {
      return;
    }
    self.isSending = YES;
    [self loadQNtoken];
    [NSObject cancelPreviousPerformRequestsWithTarget:self
                                             selector:@selector(sendTimerCheck)
                                               object:nil];
    [self performSelector:@selector(sendTimerCheck)
               withObject:nil
               afterDelay:10.0];
  }
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [NemoUtil randomString];
  [UINavigationBar appearance].translucent = NO;
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  [AppConfig statusbarStyle:YES];
}

- (void)textViewDidBeginEditing:(UITextView *)textView {
  if ([textView.text isEqualToString:@"请输入你想发布的内容"]) {
    textView.text = @"";
    textView.textColor = [UIColor blackColor];
  } else {
    if (textView.text.length > 0) {
      [self updateTextViewWithAttributedText];
    }
  }
}

- (void)textViewDidEndEditing:(UITextView *)textView {
  if (textView.text.length == 0) {
    textView.text = @"请输入你想发布的内容";
    textView.textColor = [UIColor lightGrayColor];
    [self.atUserInfos removeAllObjects];
    [self.taggedFriends removeAllObjects];
  } else {
    if (textView.text.length > 0 &&
        ![textView.text isEqualToString:@"请输入你想发布的内容"]) {
      [self updateTextViewWithAttributedText];
      [self syncTaggedFriendsWithText];
    }
  }
}

#pragma mark -hxphotoviewDelegate
- (void)removePhotoAtIndex:(NSInteger)index {
  if (index < self.selectedImages.count) {
    [self.selectedImages removeObjectAtIndex:index];
    if (index < self.selectedAssets.count) {
      [self.selectedAssets removeObjectAtIndex:index];
    }
    self.imageNum = self.selectedImages.count;
    [self updatePhotoContainerView];
    [self checkMusicAndPhotoStatus];
  }
}

#pragma mark - 草稿箱
- (BOOL)isCanDraft {
  BOOL isTextEmpty = _dyTextView.text.length == 0 ||
                     [_dyTextView.text isEqualToString:@"请输入你想发布的内容"];
  if (isTextEmpty && self.musicData == nil && self.selectedImages.count == 0) {
    return NO;
  }
  return YES;
}

- (BOOL)saveDraftBox {
  if (![self isCanDraft]) {
    return NO;
  }

  FZDraftModel *model = [[FZDraftModel alloc] init];
  model.time = [NSDate date];

  if ([_dyTextView.text isEqualToString:@"请输入你想发布的内容"]) {
    model.articleContent = @"";
  } else {
    model.articleContent = _dyTextView.text;
  }

  NSMutableArray *imgLocalIds = [NSMutableArray new];
  for (PHAsset *asset in self.selectedAssets) {
    if (asset.localIdentifier) {
      [imgLocalIds addObject:asset.localIdentifier];
    }
  }
  model.pictureArrays = [imgLocalIds copy];
  model.musicInfo = self.musicData;
  model.musicOriginText = self.originMusicText;
  model.selectTag = _choosedTag;
  model.scheduleTime = self.selectedScheduleTime;
  NSMutableArray *draftArrays =
      [[FZKeyedArchiver sharedClient] getArchiverData];
  if (!draftArrays) {
    draftArrays = [NSMutableArray array];
  }
  if (self.draftData != nil) {
    [draftArrays replaceObjectAtIndex:self.draftIndex withObject:model];
  } else {
    [draftArrays insertObject:model atIndex:0];
  }
  [[FZKeyedArchiver sharedClient] setArchiverDataWithData:draftArrays];

  return YES;
}

- (void)draftSendSuccess {
  if (self.draftData) {
    NSMutableArray *draftArrays =
        [[FZKeyedArchiver sharedClient] getArchiverData];
    [draftArrays removeObjectAtIndex:self.draftIndex];
    [[FZKeyedArchiver sharedClient] setArchiverDataWithData:draftArrays];
  }
}

#pragma mark - 监听进入后台，保存草稿
- (void)enterBackground {
  if ([self isCanDraft]) {
    self.enterbackgroundSaved = [self saveDraftBox];
  } else {
    self.enterbackgroundSaved = NO;
  }
}

- (void)enterForeground {
  if (self.enterbackgroundSaved) {
    self.enterbackgroundSaved = NO;
    [self.view makeToast:@"已自动保存到草稿箱"
                duration:2.0
                position:CSToastPositionCenter];
  }
}

- (void)sendTimerCheck {
  if (self.isSending) {
    self.isSending = NO;
    [self saveDraftBox];
    [HUD dissmiss];
    [SVProgressHUD showInfoWithStatus:@"已保存到草稿箱，请稍后重试"];
    [self dismissViewControllerAnimated:YES
                             completion:^{

                             }];
  }
}

- (BOOL)textView:(UITextView *)textView
    shouldChangeTextInRange:(NSRange)range
            replacementText:(NSString *)text {
  BOOL isDefaultText = [textView.text isEqualToString:@"请输入你想发布的内容"];

  if (isDefaultText && text.length > 0) {
    textView.text = @"";
    textView.textColor = [UIColor blackColor];

    if ([text isEqualToString:@"@"]) {
      range = NSMakeRange(0, 0);
      self.atSymbolRange = range;
      [self showFriendSelectionVC];
      return NO;
    }

    return YES;
  }

  if ([text isEqualToString:@"@"]) {
    self.atSymbolRange = range;
    [self showFriendSelectionVC];
    return NO;
  }

  if (range.location >= 2000) {
    return NO;
  }

  __weak typeof(self) weakSelf = self;
  dispatch_async(dispatch_get_main_queue(), ^{
    if (textView.markedTextRange == nil) {
      NSLog(@"🟠 文本变化，调用同步方法");
      [weakSelf updateTextViewWithAttributedText];
      [weakSelf syncTaggedFriendsWithText];
    }
  });

  return YES;
}

- (void)showFriendSelectionVC {
  [self.dyTextView resignFirstResponder];

  FriendSelectionVC *friendVC = [[FriendSelectionVC alloc] init];
  friendVC.delegate = self;

  // 从atUserInfos中提取当前已选择的用户信息
  NSMutableArray *selectedFriends = [NSMutableArray array];
  for (AtUserInfo *userInfo in self.atUserInfos) {
    // 创建AttentionUserResult对象用于选择界面显示
    AttentionUserResult *friend = [[AttentionUserResult alloc] init];
    friend.userid = userInfo.userid;
    friend.nickname = userInfo.nickname;
    [selectedFriends addObject:friend];
  }
  friendVC.selectedFriends = [selectedFriends copy];

  UINavigationController *nav =
      [[UINavigationController alloc] initWithRootViewController:friendVC];
  nav.modalPresentationStyle = UIModalPresentationFullScreen;
  [self presentViewController:nav animated:YES completion:nil];
}

#pragma mark - FriendSelectionDelegate
- (void)didSelectFriends:(NSArray *)friendsInfo {
  NSLog(@"🔵 didSelectFriends 开始");
  NSLog(@"🔵 选择界面返回的用户数量: %lu", (unsigned long)friendsInfo.count);
  for (AttentionUserResult *friend in friendsInfo) {
    NSLog(@"🔵 选择的用户: %@ (ID: %@)", friend.nickname, friend.userid);
  }

  NSLog(@"🔵 当前atUserInfos数量: %lu", (unsigned long)self.atUserInfos.count);
  for (AtUserInfo *info in self.atUserInfos) {
    NSLog(@"🔵 现有用户: %@ (ID: %@, 位置: %@)", info.nickname, info.userid,
          NSStringFromRange(info.textRange));
  }

  if (friendsInfo.count == 0) {
    [self.dyTextView becomeFirstResponder];
    return;
  }

  // 找出本次新选择的用户
  NSMutableArray *newSelectedFriends = [NSMutableArray array];
  for (AttentionUserResult *selectedFriend in friendsInfo) {
    BOOL isExisting = NO;
    for (AtUserInfo *existingUserInfo in self.atUserInfos) {
      if ([selectedFriend.userid isEqualToString:existingUserInfo.userid]) {
        isExisting = YES;
        break;
      }
    }
    if (!isExisting) {
      [newSelectedFriends addObject:selectedFriend];
    }
  }

  NSLog(@"🔵 新选择的用户数量: %lu", (unsigned long)newSelectedFriends.count);
  for (AttentionUserResult *friend in newSelectedFriends) {
    NSLog(@"🔵 新用户: %@ (ID: %@)", friend.nickname, friend.userid);
  }

  // 如果没有新选择的用户，直接返回
  if (newSelectedFriends.count == 0) {
    NSLog(@"🔵 没有新用户，直接返回");
    [self.dyTextView becomeFirstResponder];
    return;
  }

  // 为新选择的用户生成@文本
  NSMutableString *atString = [NSMutableString string];
  for (AttentionUserResult *friend in newSelectedFriends) {
    [atString appendFormat:@"@%@ ", friend.nickname];
  }
  NSLog(@"🔵 生成的@文本: '%@'", atString);

  NSString *originalText = self.dyTextView.text;
  NSLog(@"🔵 原始文本: '%@'", originalText);
  BOOL isDefaultText = [originalText isEqualToString:@"请输入你想发布的内容"];

  if (isDefaultText) {
    originalText = @"";
  }

  NSMutableString *newText = [NSMutableString stringWithString:originalText];
  NSUInteger insertPosition = self.atSymbolRange.location;
  NSLog(@"🔵 插入位置: %lu", (unsigned long)insertPosition);

  if (insertPosition < newText.length &&
      [newText characterAtIndex:insertPosition] == '@') {
    [newText deleteCharactersInRange:NSMakeRange(insertPosition, 1)];
    NSLog(@"🔵 删除了@符号");
  }

  [newText insertString:atString atIndex:insertPosition];
  NSLog(@"🔵 插入后的文本: '%@'", newText);

  // 为新添加的用户创建AtUserInfo对象
  NSUInteger currentPosition = insertPosition;
  for (AttentionUserResult *friend in newSelectedFriends) {
    AtUserInfo *userInfo = [[AtUserInfo alloc] init];
    userInfo.userid = friend.userid;
    userInfo.nickname = friend.nickname;

    NSString *atUserText = [NSString stringWithFormat:@"@%@ ", friend.nickname];
    userInfo.textRange = NSMakeRange(currentPosition, atUserText.length);
    [self.atUserInfos addObject:userInfo];
    NSLog(@"🔵 添加AtUserInfo: %@ (ID: %@, 位置: %@)", userInfo.nickname,
          userInfo.userid, NSStringFromRange(userInfo.textRange));

    currentPosition += atUserText.length;
  }

  NSLog(@"🔵 添加后atUserInfos总数: %lu",
        (unsigned long)self.atUserInfos.count);

  [self applyAttributedTextForAtUsers:newText];

  // 手动调用同步，确保数据一致性
  [self syncTaggedFriendsWithText];

  NSUInteger newCursorPosition = insertPosition + atString.length;
  UITextPosition *newPosition =
      [self.dyTextView positionFromPosition:self.dyTextView.beginningOfDocument
                                     offset:newCursorPosition];
  self.dyTextView.selectedTextRange =
      [self.dyTextView textRangeFromPosition:newPosition
                                  toPosition:newPosition];

  [self.dyTextView becomeFirstResponder];
}

- (void)uploadGifData:(NSData *)gifData imgIndex:(NSInteger)imgIndex {
  @weakify(self);
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];
  NSString *imgKey = [NSString
      stringWithFormat:@"%@%@%@", [NemoUtil randomString], CurrentUser.userid,
                       [NemoUtil getNowTimeIntervalStr]];
  NSString *DocumentsPath =
      [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];
  NSString *ImagePath =
      [NSString stringWithFormat:@"/theGifImage_%ld.gif", (long)imgIndex];
  NSString *filePath = [DocumentsPath stringByAppendingString:ImagePath];
  [[NSFileManager defaultManager] createFileAtPath:filePath
                                          contents:gifData
                                        attributes:nil];
  [upManager
       putFile:filePath
           key:imgKey
         token:self.imageToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        @strongify(self);
        if (info.ok) {
          [self.tempUpImgArray replaceObjectAtIndex:imgIndex withObject:imgKey];
          self.imageNum = self.imageNum - 1;
          if (self.imageNum == 0) {
            for (NSString *tepImg in [self.tempUpImgArray copy]) {
              if ([tepImg isEqualToString:@"_"]) {
                [self.tempUpImgArray removeObject:tepImg];
              }
            }
            [self loadPostDy];
          }
        } else {
          self.isSending = NO;
          [HUD dissmiss];
          [self.view makeToast:@"发送失败,请检查网络连接后重试"
                      duration:1.0
                      position:CSToastPositionCenter];
        }
      }
        option:option];
}

#pragma mark - 富文本处理
- (void)updateTextViewWithAttributedText {
  if (self.dyTextView.markedTextRange != nil) {
    return;
  }

  NSString *text = self.dyTextView.text;
  if (text.length == 0) {
    return;
  }

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:text];

  UIFont *initialFont = [UIFont systemFontOfSize:14 * kMainTemp];

  [attributedString addAttribute:NSForegroundColorAttributeName
                           value:[UIColor blackColor]
                           range:NSMakeRange(0, text.length)];
  [attributedString addAttribute:NSFontAttributeName
                           value:initialFont
                           range:NSMakeRange(0, text.length)];

  NSError *error = nil;
  NSRegularExpression *regex = [NSRegularExpression
      regularExpressionWithPattern:@"@[^@\\s]+\\s"
                           options:NSRegularExpressionCaseInsensitive
                             error:&error];

  if (!error) {
    NSArray *matches = [regex matchesInString:text
                                      options:0
                                        range:NSMakeRange(0, text.length)];

    for (NSTextCheckingResult *match in matches) {
      [attributedString addAttribute:NSForegroundColorAttributeName
                               value:kAtUserTextColor
                               range:match.range];
      [attributedString
          addAttribute:NSFontAttributeName
                 value:SourceHanSerifBoldFont(initialFont.pointSize)
                 range:match.range];
    }
  }

  UITextRange *currentRange = self.dyTextView.selectedTextRange;

  self.dyTextView.attributedText = attributedString;

  if (currentRange) {
    self.dyTextView.selectedTextRange = currentRange;
  }
}

- (void)applyAttributedTextForAtUsers:(NSString *)text {
  if (self.dyTextView.markedTextRange != nil) {
    return;
  }

  if (text.length == 0) {
    return;
  }

  NSMutableAttributedString *attributedString =
      [[NSMutableAttributedString alloc] initWithString:text];
  UIFont *initialFont = [UIFont systemFontOfSize:14 * kMainTemp];
  [attributedString addAttribute:NSForegroundColorAttributeName
                           value:[UIColor blackColor]
                           range:NSMakeRange(0, text.length)];
  [attributedString addAttribute:NSFontAttributeName
                           value:initialFont
                           range:NSMakeRange(0, text.length)];

  NSError *error = nil;
  NSRegularExpression *regex = [NSRegularExpression
      regularExpressionWithPattern:@"@[^@\\s]+\\s"
                           options:NSRegularExpressionCaseInsensitive
                             error:&error];

  if (!error) {
    NSArray *matches = [regex matchesInString:text
                                      options:0
                                        range:NSMakeRange(0, text.length)];

    for (NSTextCheckingResult *match in matches) {
      [attributedString addAttribute:NSForegroundColorAttributeName
                               value:kAtUserTextColor
                               range:match.range];
      [attributedString
          addAttribute:NSFontAttributeName
                 value:SourceHanSerifBoldFont(initialFont.pointSize)
                 range:match.range];
    }
  }

  UITextRange *currentRange = self.dyTextView.selectedTextRange;

  self.dyTextView.attributedText = attributedString;

  if (currentRange) {
    self.dyTextView.selectedTextRange = currentRange;
  }
}

// 同步文本中的@用户和数组
- (void)syncTaggedFriendsWithText {
  NSLog(@"🟡 syncTaggedFriendsWithText 开始");
  NSString *text = self.dyTextView.text;
  NSLog(@"🟡 当前文本: '%@'", text);

  if (text.length == 0) {
    NSLog(@"🟡 文本为空，清空所有数组");
    [self.atUserInfos removeAllObjects];
    [self.taggedFriends removeAllObjects];
    return;
  }

  // 使用正则表达式找出所有@用户名（匹配@符号后跟非空白字符，直到空格或结尾）
  NSError *error = nil;
  NSRegularExpression *regex = [NSRegularExpression
      regularExpressionWithPattern:@"@([^@\\s]+)"
                           options:NSRegularExpressionCaseInsensitive
                             error:&error];

  if (error) {
    NSLog(@"🟡 正则表达式错误: %@", error);
    return;
  }

  NSArray *matches = [regex matchesInString:text
                                    options:0
                                      range:NSMakeRange(0, text.length)];

  NSLog(@"🟡 正则匹配到 %lu 个@用户", (unsigned long)matches.count);

  // 如果没有@用户，清空数组
  if (matches.count == 0) {
    NSLog(@"🟡 没有匹配到@用户，清空数组");
    [self.atUserInfos removeAllObjects];
    [self.taggedFriends removeAllObjects];
    return;
  }

  // 创建新的AtUserInfo数组
  NSMutableArray<AtUserInfo *> *newAtUserInfos = [NSMutableArray array];

  for (NSTextCheckingResult *match in matches) {
    NSRange fullRange = [match range];
    NSRange usernameRange = [match rangeAtIndex:1]; // 第一个捕获组是用户名
    NSString *username = [text substringWithRange:usernameRange];
    NSLog(@"🟡 匹配到用户名: '%@', 完整范围: %@", username,
          NSStringFromRange(fullRange));

    // 查找对应的用户信息
    AtUserInfo *existingUserInfo = nil;
    for (AtUserInfo *userInfo in self.atUserInfos) {
      if ([userInfo.nickname isEqualToString:username]) {
        existingUserInfo = userInfo;
        NSLog(@"🟡 找到现有用户信息: %@ (ID: %@)", userInfo.nickname,
              userInfo.userid);
        break;
      }
    }

    if (existingUserInfo) {
      // 更新位置信息
      existingUserInfo.textRange = fullRange;
      [newAtUserInfos addObject:existingUserInfo];
      NSLog(@"🟡 更新用户位置: %@ -> %@", existingUserInfo.nickname,
            NSStringFromRange(fullRange));
    } else {
      NSLog(@"🟡 未找到用户信息: %@", username);
    }
  }

  NSLog(@"🟡 同步前atUserInfos数量: %lu",
        (unsigned long)self.atUserInfos.count);
  NSLog(@"🟡 同步后newAtUserInfos数量: %lu",
        (unsigned long)newAtUserInfos.count);

  // 更新数组
  [self.atUserInfos removeAllObjects];
  [self.atUserInfos addObjectsFromArray:newAtUserInfos];

  // 更新taggedFriends数组以保持兼容性
  [self updateTaggedFriendsFromAtUserInfos];
  NSLog(@"🟡 syncTaggedFriendsWithText 结束");
}

// 根据位置排序更新taggedFriends数组
- (void)updateTaggedFriendsFromAtUserInfos {
  NSLog(@"🟢 updateTaggedFriendsFromAtUserInfos 开始");
  NSLog(@"🟢 当前atUserInfos数量: %lu", (unsigned long)self.atUserInfos.count);

  // 按照文本位置排序
  NSArray *sortedUserInfos = [self.atUserInfos
      sortedArrayUsingComparator:^NSComparisonResult(AtUserInfo *obj1,
                                                     AtUserInfo *obj2) {
        if (obj1.textRange.location < obj2.textRange.location) {
          return NSOrderedAscending;
        } else if (obj1.textRange.location > obj2.textRange.location) {
          return NSOrderedDescending;
        }
        return NSOrderedSame;
      }];

  NSLog(@"🟢 排序后的用户:");
  for (AtUserInfo *userInfo in sortedUserInfos) {
    NSLog(@"🟢 用户: %@ (ID: %@, 位置: %@)", userInfo.nickname, userInfo.userid,
          NSStringFromRange(userInfo.textRange));
  }

  // 更新taggedFriends数组
  [self.taggedFriends removeAllObjects];
  for (AtUserInfo *userInfo in sortedUserInfos) {
    AttentionUserResult *friend = [[AttentionUserResult alloc] init];
    friend.userid = userInfo.userid;
    friend.nickname = userInfo.nickname;
    [self.taggedFriends addObject:friend];
  }

  NSLog(@"🟢 最终taggedFriends数组:");
  for (AttentionUserResult *friend in self.taggedFriends) {
    NSLog(@"🟢 用户: %@ (ID: %@)", friend.nickname, friend.userid);
  }
  NSLog(@"🟢 updateTaggedFriendsFromAtUserInfos 结束");
}

@end
